import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define supported languages
export type SupportedLanguage = 
  | 'ar' // Arabic
  | 'en' // English
  | 'fr' // French
  | 'de' // German
  | 'es' // Spanish
  | 'zh' // Chinese (Simplified)
  | 'zh-Hant' // Chinese (Traditional)
  | 'ja' // Japanese
  | 'ko' // Korean
  | 'ru' // Russian
  | 'pt'; // Portuguese

interface LanguageState {
  currentLanguage: SupportedLanguage;
  isRTL: boolean;
}

// Helper function to determine if a language is RTL
const isRTLLanguage = (lang: SupportedLanguage): boolean => {
  return lang === 'ar';
};

const initialState: LanguageState = {
  currentLanguage: 'en',
  isRTL: false,
};

const languageSlice = createSlice({
  name: 'language',
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<SupportedLanguage>) => {
      state.currentLanguage = action.payload;
      state.isRTL = isRTLLanguage(action.payload);
    },
  },
});

export const { setLanguage } = languageSlice.actions;

export default languageSlice.reducer;