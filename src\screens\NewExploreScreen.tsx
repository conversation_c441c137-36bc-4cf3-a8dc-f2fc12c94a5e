import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  FlatList,
  Image,
  TouchableOpacity,
  Platform,
} from 'react-native';
import {
  Searchbar,
  Card,
  IconButton,
  Surface,
} from 'react-native-paper';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { useTranslation } from '../i18n';

// Mock data for categories and products
const categories = [
  { id: '1', name: 'Fashion', nameAr: 'أزياء', icon: '👗', color: '#FF6B6B' },
  { id: '2', name: 'Electronics', nameAr: 'إلكترونيات', icon: '📱', color: '#4ECDC4' },
  { id: '3', name: 'Home', nameAr: 'منزل', icon: '🏠', color: '#45B7D1' },
  { id: '4', name: 'Beauty', nameAr: 'جمال', icon: '💄', color: '#F7DC6F' },
  { id: '5', name: 'Sports', nameAr: 'رياضة', icon: '⚽', color: '#BB8FCE' },
  { id: '6', name: 'Books', nameAr: 'كتب', icon: '📚', color: '#82E0AA' },
];

const products = [
  {
    id: '1',
    name: 'Wireless Headphones',
    nameAr: 'سماعات لاسلكية',
    price: 99.99,
    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300',
    category: 'Electronics',
    rating: 4.5,
    seller: 'TechStore',
  },
  {
    id: '2',
    name: 'Summer Dress',
    nameAr: 'فستان صيفي',
    price: 49.99,
    image: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=300',
    category: 'Fashion',
    rating: 4.8,
    seller: 'FashionHub',
  },
  {
    id: '3',
    name: 'Coffee Maker',
    nameAr: 'ماكينة قهوة',
    price: 129.99,
    image: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300',
    category: 'Home',
    rating: 4.3,
    seller: 'HomeGoods',
  },
  {
    id: '4',
    name: 'Skincare Set',
    nameAr: 'مجموعة العناية بالبشرة',
    price: 79.99,
    image: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=300',
    category: 'Beauty',
    rating: 4.7,
    seller: 'BeautyWorld',
  },
];

const NewExploreScreen = () => {
  const theme = useSelector((state: RootState) => state.theme);
  const language = useSelector((state: RootState) => state.language);
  const { t } = useTranslation();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.nameAr.includes(searchQuery);
    const matchesCategory = !selectedCategory || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const renderCategoryItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[
        styles.categoryCard,
        { backgroundColor: item.color },
        selectedCategory === item.name && styles.selectedCategory
      ]}
      onPress={() => setSelectedCategory(selectedCategory === item.name ? null : item.name)}
    >
      <Text style={styles.categoryIcon}>{item.icon}</Text>
      <Text style={styles.categoryName}>
        {language.currentLanguage === 'ar' ? item.nameAr : item.name}
      </Text>
    </TouchableOpacity>
  );

  const renderProductItem = ({ item }: { item: any }) => (
    <Card style={[styles.productCard, { backgroundColor: theme.theme.colors.surface }]}>
      <Image source={{ uri: item.image }} style={styles.productImage} />
      <View style={styles.productInfo}>
        <Text style={[styles.productName, { color: theme.theme.colors.onSurface }]}>
          {language.currentLanguage === 'ar' ? item.nameAr : item.name}
        </Text>
        <Text style={[styles.productPrice, { color: theme.theme.colors.primary }]}>
          ${item.price}
        </Text>
        <View style={styles.productMeta}>
          <Text style={[styles.productRating, { color: theme.theme.colors.onSurfaceVariant }]}>
            ⭐ {item.rating}
          </Text>
          <Text style={[styles.productSeller, { color: theme.theme.colors.onSurfaceVariant }]}>
            {item.seller}
          </Text>
        </View>
      </View>
      <IconButton
        icon="heart-outline"
        size={20}
        style={styles.favoriteButton}
        iconColor={theme.theme.colors.primary}
      />
    </Card>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.theme.colors.background }]}>
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.theme.colors.surface }]}>
        <Text style={[styles.headerTitle, { color: theme.theme.colors.onSurface }]}>
          {t('explore')}
        </Text>
        <Searchbar
          placeholder={language.currentLanguage === 'ar' ? 'ابحث عن المنتجات...' : 'Search products...'}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />
      </Surface>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Categories */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.theme.colors.onSurface }]}>
            {language.currentLanguage === 'ar' ? 'الفئات' : 'Categories'}
          </Text>
          <FlatList
            data={categories}
            renderItem={renderCategoryItem}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>

        {/* Products */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.theme.colors.onSurface }]}>
            {selectedCategory 
              ? `${selectedCategory} ${language.currentLanguage === 'ar' ? 'المنتجات' : 'Products'}`
              : language.currentLanguage === 'ar' ? 'جميع المنتجات' : 'All Products'
            }
          </Text>
          <FlatList
            data={filteredProducts}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id}
            numColumns={2}
            columnWrapperStyle={styles.productRow}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: Platform.OS === 'web' ? 20 : 50,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  searchBar: {
    elevation: 0,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  categoriesList: {
    paddingRight: 20,
  },
  categoryCard: {
    padding: 15,
    borderRadius: 15,
    marginRight: 15,
    alignItems: 'center',
    minWidth: 80,
  },
  selectedCategory: {
    transform: [{ scale: 1.1 }],
    elevation: 8,
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 5,
  },
  categoryName: {
    fontSize: 12,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  productRow: {
    justifyContent: 'space-between',
  },
  productCard: {
    flex: 1,
    margin: 5,
    borderRadius: 15,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  productImage: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  productInfo: {
    padding: 10,
  },
  productName: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  productMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  productRating: {
    fontSize: 12,
  },
  productSeller: {
    fontSize: 10,
  },
  favoriteButton: {
    position: 'absolute',
    top: 5,
    right: 5,
  },
});

export default NewExploreScreen;
