import * as Localization from 'expo-localization';
import { I18n } from 'i18n-js';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import translations from './simpleTranslations';
import { SupportedLanguage } from '../store/slices/languageSlice';

// Create i18n instance
const i18n = new I18n(translations);

// Set the locale once at the beginning of your app
i18n.locale = Localization.locale;

// When a value is missing from a language it'll fallback to another language with the key present
i18n.enableFallback = true;
i18n.defaultLocale = 'en';

// Hook to use translations with Redux language state
export const useTranslation = () => {
  const { currentLanguage, isRTL } = useSelector((state: RootState) => state.language as { currentLanguage: string; isRTL: boolean });
  
  // Set the locale from Redux state
  i18n.locale = currentLanguage;
  
  // Return the translate function and RTL status
  return {
    t: (key: string, options?: object) => i18n.t(key, options),
    isRTL,
    locale: currentLanguage,
    changeLanguage: (language: SupportedLanguage) => {
      i18n.locale = language;
    },
  };
};

export default i18n;