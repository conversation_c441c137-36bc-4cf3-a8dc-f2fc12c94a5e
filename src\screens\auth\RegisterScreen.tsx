import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {
  TextInput,
  Button,
  Surface,
  IconButton,
  Checkbox,
  Divider,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { loginUser } from '../../store/slices/userSlice';
import { useTranslation } from '../../i18n';

const RegisterScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const theme = useSelector((state: RootState) => state.theme);
  const { loading } = useSelector((state: RootState) => state.user);
  const { t } = useTranslation();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    if (!formData.name || !formData.email || !formData.password || !formData.confirmPassword) {
      Alert.alert(t('error'), t('fillAllFields'));
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert(t('error'), t('passwordsDoNotMatch'));
      return false;
    }

    if (formData.password.length < 6) {
      Alert.alert(t('error'), t('passwordTooShort'));
      return false;
    }

    if (!agreeToTerms) {
      Alert.alert(t('error'), t('agreeToTerms'));
      return false;
    }

    return true;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    try {
      // Simulate registration API call
      const userData = {
        id: '1',
        email: formData.email,
        name: formData.name,
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
        isVerified: false,
      };

      dispatch(loginUser(userData));
      Alert.alert(t('success'), t('accountCreated'));
    } catch (error) {
      Alert.alert(t('error'), t('registrationFailed'));
    }
  };

  const handleSocialRegister = (provider: string) => {
    Alert.alert(t('comingSoon'), `${provider} ${t('registrationComingSoon')}`);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={() => navigation.goBack()}
            iconColor={theme.theme.colors.onSurface}
          />
          <Text style={[styles.title, { color: theme.theme.colors.onSurface }]}>
            {t('signUp')}
          </Text>
          <View style={{ width: 40 }} />
        </View>

        {/* Registration Form */}
        <Surface style={[styles.formCard, { backgroundColor: theme.theme.colors.surface }]}>
          <Text style={[styles.welcomeText, { color: theme.theme.colors.onSurface }]}>
            {t('createAccount')}
          </Text>
          <Text style={[styles.subtitleText, { color: theme.theme.colors.onSurfaceVariant }]}>
            {t('joinSaleStory')}
          </Text>

          <TextInput
            label={t('fullName')}
            value={formData.name}
            onChangeText={(value) => handleInputChange('name', value)}
            mode="outlined"
            style={styles.input}
            left={<TextInput.Icon icon="account" />}
          />

          <TextInput
            label={t('email')}
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            mode="outlined"
            keyboardType="email-address"
            autoCapitalize="none"
            style={styles.input}
            left={<TextInput.Icon icon="email" />}
          />

          <TextInput
            label={t('password')}
            value={formData.password}
            onChangeText={(value) => handleInputChange('password', value)}
            mode="outlined"
            secureTextEntry={!showPassword}
            style={styles.input}
            left={<TextInput.Icon icon="lock" />}
            right={
              <TextInput.Icon
                icon={showPassword ? 'eye-off' : 'eye'}
                onPress={() => setShowPassword(!showPassword)}
              />
            }
          />

          <TextInput
            label={t('confirmPassword')}
            value={formData.confirmPassword}
            onChangeText={(value) => handleInputChange('confirmPassword', value)}
            mode="outlined"
            secureTextEntry={!showConfirmPassword}
            style={styles.input}
            left={<TextInput.Icon icon="lock-check" />}
            right={
              <TextInput.Icon
                icon={showConfirmPassword ? 'eye-off' : 'eye'}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              />
            }
          />

          {/* Terms and Conditions */}
          <View style={styles.termsContainer}>
            <Checkbox
              status={agreeToTerms ? 'checked' : 'unchecked'}
              onPress={() => setAgreeToTerms(!agreeToTerms)}
            />
            <Text style={[styles.termsText, { color: theme.theme.colors.onSurfaceVariant }]}>
              {t('agreeToTermsText')}
            </Text>
          </View>

          <Button
            mode="contained"
            onPress={handleRegister}
            loading={loading}
            disabled={loading}
            style={styles.registerButton}
            contentStyle={styles.buttonContent}
          >
            {t('createAccount')}
          </Button>

          <Divider style={styles.divider} />

          {/* Social Registration */}
          <Text style={[styles.socialText, { color: theme.theme.colors.onSurfaceVariant }]}>
            {t('orSignUpWith')}
          </Text>

          <View style={styles.socialButtons}>
            <Button
              mode="outlined"
              onPress={() => handleSocialRegister('Google')}
              style={styles.socialButton}
              icon="google"
            >
              Google
            </Button>
            <Button
              mode="outlined"
              onPress={() => handleSocialRegister('Facebook')}
              style={styles.socialButton}
              icon="facebook"
            >
              Facebook
            </Button>
          </View>

          {/* Sign In Link */}
          <View style={styles.signInContainer}>
            <Text style={[styles.signInText, { color: theme.theme.colors.onSurfaceVariant }]}>
              {t('alreadyHaveAccount')}
            </Text>
            <Button
              mode="text"
              onPress={() => navigation.navigate('Login' as never)}
              compact
            >
              {t('signIn')}
            </Button>
          </View>
        </Surface>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 30,
    marginTop: Platform.OS === 'web' ? 20 : 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  formCard: {
    borderRadius: 20,
    padding: 30,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  welcomeText: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitleText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
  },
  input: {
    marginBottom: 15,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  termsText: {
    flex: 1,
    fontSize: 14,
    marginLeft: 8,
  },
  registerButton: {
    marginBottom: 20,
    borderRadius: 25,
  },
  buttonContent: {
    height: 50,
  },
  divider: {
    marginVertical: 20,
  },
  socialText: {
    textAlign: 'center',
    marginBottom: 15,
  },
  socialButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  socialButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  signInContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signInText: {
    fontSize: 14,
  },
});

export default RegisterScreen;
