import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Dimensions,
  ActivityIndicator,
  Text,
  ViewToken,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import {
  fetchStoriesStart,
  fetchStoriesSuccess,
  fetchStoriesFailure,
  Story,
} from '../store/slices/storySlice';
import StoryCard from '../components/StoryCard';
import FilterBar from '../components/FilterBar';
import { useTranslation } from '../i18n';

const { width, height } = Dimensions.get('window');

// Mock data for stories
const mockStories: Story[] = [
  {
    id: '1',
    userId: 'user1',
    username: 'fashionista',
    userAvatar: 'https://randomuser.me/api/portraits/women/43.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1539109136881-3be0616acf4b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod1',
      name: 'Summer Dress Collection',
      price: 59.99,
      currency: 'USD',
      description: 'Beautiful summer dress perfect for beach days',
      category: 'fashion',
    },
    likes: 1245,
    views: 5678,
    shares: 123,
    saved: false,
    liked: false,
    createdAt: new Date().toISOString(),
    location: 'New York',
    tags: ['fashion', 'summer', 'dress'],
  },
  {
    id: '2',
    userId: 'user2',
    username: 'techgadgets',
    userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-**********-7041f2a55e12?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod2',
      name: 'Smart Watch Pro',
      price: 199.99,
      currency: 'USD',
      description: 'Latest smartwatch with health tracking features',
      category: 'electronics',
    },
    likes: 876,
    views: 3421,
    shares: 76,
    saved: false,
    liked: false,
    createdAt: new Date().toISOString(),
    location: 'San Francisco',
    tags: ['tech', 'gadgets', 'smartwatch'],
  },
  {
    id: '3',
    userId: 'user3',
    username: 'homedesign',
    userAvatar: 'https://randomuser.me/api/portraits/women/65.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod3',
      name: 'Modern Coffee Table',
      price: 149.99,
      currency: 'USD',
      description: 'Elegant coffee table for your living room',
      category: 'home',
    },
    likes: 543,
    views: 2198,
    shares: 45,
    saved: false,
    liked: false,
    createdAt: new Date().toISOString(),
    location: 'Los Angeles',
    tags: ['furniture', 'home', 'design'],
  },
];

const HomeScreen = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { stories, loading, error, filters } = useSelector((state: RootState) => state.stories as { stories: Story[]; loading: boolean; error: string | null; filters: any });
  
  const [activeStoryIndex, setActiveStoryIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  // Fetch stories on component mount
  useEffect(() => {
    fetchStories();
  }, []);

  // Re-fetch stories when filters change
  useEffect(() => {
    fetchStories();
  }, [filters]);

  const fetchStories = () => {
    dispatch(fetchStoriesStart());
    
    // Simulate API call with timeout
    setTimeout(() => {
      try {
        // Filter stories based on current filters
        let filteredStories = [...mockStories];
        
        if (filters.category) {
          filteredStories = filteredStories.filter(
            (story) => story.product.category === filters.category
          );
        }
        
        if (filters.priceRange) {
          filteredStories = filteredStories.filter(
            (story) =>
              story.product.price >= filters.priceRange![0] &&
              story.product.price <= filters.priceRange![1]
          );
        }
        
        if (filters.location) {
          filteredStories = filteredStories.filter(
            (story) => story.location?.includes(filters.location!)
          );
        }
        
        dispatch(fetchStoriesSuccess(filteredStories));
      } catch (err) {
        dispatch(fetchStoriesFailure('Failed to fetch stories'));
      }
    }, 1000);
  };

  const handleViewableItemsChanged = useRef(({ viewableItems }: { viewableItems: ViewToken[] }) => {
    if (viewableItems.length > 0) {
      setActiveStoryIndex(viewableItems[0].index || 0);
    }
  }).current;

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50,
  }).current;

  const renderStoryCard = ({ item, index }: { item: Story; index: number }) => (
    <StoryCard
      story={item}
      isActive={index === activeStoryIndex}
      onComplete={() => {
        // Move to next story when current one completes
        if (index < stories.length - 1) {
          flatListRef.current?.scrollToIndex({ index: index + 1, animated: true });
        }
      }}
    />
  );

  if (loading && stories.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6200ee" />
        <Text style={styles.loadingText}>{t('loading')}</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <Text style={styles.retryText} onPress={fetchStories}>
          {t('tryAgain')}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FilterBar />
      
      {stories.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>{t('noResults')}</Text>
        </View>
      ) : (
        <FlatList
          ref={flatListRef}
          data={stories}
          renderItem={renderStoryCard}
          keyExtractor={(item) => item.id}
          pagingEnabled
          showsVerticalScrollIndicator={false}
          onViewableItemsChanged={handleViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
          initialNumToRender={1}
          maxToRenderPerBatch={2}
          windowSize={3}
          getItemLayout={(_, index) => ({
            length: height - 50, // Subtract tab bar height
            offset: (height - 50) * index,
            index,
          })}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff3b30',
    textAlign: 'center',
    marginBottom: 10,
  },
  retryText: {
    fontSize: 16,
    color: '#6200ee',
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  },
});

export default HomeScreen;