import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Platform,
  Alert,
} from 'react-native';
import {
  Surface,
  Button,
  IconButton,
  Switch,
  Divider,
  List,
} from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store';
import { useTranslation } from '../i18n';
import { toggleTheme } from '../store/slices/themeSlice';
import { setLanguage } from '../store/slices/languageSlice';
import { logoutUser } from '../store/slices/userSlice';

const NewProfileScreen = () => {
  const theme = useSelector((state: RootState) => state.theme);
  const language = useSelector((state: RootState) => state.language);
  const user = useSelector((state: RootState) => state.user);
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const [showStats, setShowStats] = useState(true);

  const handleThemeToggle = () => {
    dispatch(toggleTheme());
  };

  const handleLanguageToggle = () => {
    const newLanguage = language.currentLanguage === 'ar' ? 'en' : 'ar';
    dispatch(setLanguage(newLanguage));
  };

  const handleLogout = () => {
    Alert.alert(
      language.currentLanguage === 'ar' ? 'تسجيل الخروج' : 'Logout',
      language.currentLanguage === 'ar' ? 'هل أنت متأكد من تسجيل الخروج؟' : 'Are you sure you want to logout?',
      [
        {
          text: t('cancel'),
          style: 'cancel'
        },
        {
          text: language.currentLanguage === 'ar' ? 'تسجيل الخروج' : 'Logout',
          style: 'destructive',
          onPress: () => dispatch(logoutUser())
        }
      ]
    );
  };

  const stats = [
    {
      label: language.currentLanguage === 'ar' ? 'المنتجات' : 'Products',
      value: '24',
      icon: '📦'
    },
    {
      label: language.currentLanguage === 'ar' ? 'المبيعات' : 'Sales',
      value: '156',
      icon: '💰'
    },
    {
      label: language.currentLanguage === 'ar' ? 'المتابعون' : 'Followers',
      value: '1.2K',
      icon: '👥'
    },
    {
      label: language.currentLanguage === 'ar' ? 'التقييم' : 'Rating',
      value: '4.8',
      icon: '⭐'
    }
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.theme.colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Surface style={[styles.header, { backgroundColor: theme.theme.colors.surface }]}>
          <View style={styles.headerContent}>
            <Text style={[styles.headerTitle, { color: theme.theme.colors.onSurface }]}>
              {t('profile')}
            </Text>
            <IconButton
              icon="cog"
              size={24}
              iconColor={theme.theme.colors.onSurface}
              onPress={() => Alert.alert(t('comingSoon'), 'Settings coming soon')}
            />
          </View>
        </Surface>

        {/* Profile Info */}
        <Surface style={[styles.profileSection, { backgroundColor: theme.theme.colors.surface }]}>
          <View style={styles.profileHeader}>
            <Image 
              source={{ uri: user.user?.avatar || 'https://randomuser.me/api/portraits/men/1.jpg' }} 
              style={styles.avatar} 
            />
            <View style={styles.profileInfo}>
              <Text style={[styles.userName, { color: theme.theme.colors.onSurface }]}>
                {user.user?.name || 'John Doe'}
              </Text>
              <Text style={[styles.userEmail, { color: theme.theme.colors.onSurfaceVariant }]}>
                {user.user?.email || '<EMAIL>'}
              </Text>
              {user.user?.isVerified && (
                <View style={styles.verifiedBadge}>
                  <Text style={styles.verifiedText}>
                    ✓ {language.currentLanguage === 'ar' ? 'موثق' : 'Verified'}
                  </Text>
                </View>
              )}
            </View>
            <IconButton
              icon="pencil"
              size={20}
              iconColor={theme.theme.colors.primary}
              onPress={() => Alert.alert(t('comingSoon'), 'Edit profile coming soon')}
            />
          </View>

          <Button
            mode="outlined"
            onPress={() => Alert.alert(t('comingSoon'), 'View full profile coming soon')}
            style={styles.viewProfileButton}
          >
            {language.currentLanguage === 'ar' ? 'عرض الملف الشخصي' : 'View Profile'}
          </Button>
        </Surface>

        {/* Stats */}
        {showStats && (
          <Surface style={[styles.statsSection, { backgroundColor: theme.theme.colors.surface }]}>
            <View style={styles.statsHeader}>
              <Text style={[styles.sectionTitle, { color: theme.theme.colors.onSurface }]}>
                {language.currentLanguage === 'ar' ? 'الإحصائيات' : 'Statistics'}
              </Text>
              <IconButton
                icon={showStats ? 'chevron-up' : 'chevron-down'}
                size={20}
                onPress={() => setShowStats(!showStats)}
              />
            </View>
            <View style={styles.statsGrid}>
              {stats.map((stat, index) => (
                <View key={index} style={styles.statItem}>
                  <Text style={styles.statIcon}>{stat.icon}</Text>
                  <Text style={[styles.statValue, { color: theme.theme.colors.primary }]}>
                    {stat.value}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.theme.colors.onSurfaceVariant }]}>
                    {stat.label}
                  </Text>
                </View>
              ))}
            </View>
          </Surface>
        )}

        {/* Settings */}
        <Surface style={[styles.settingsSection, { backgroundColor: theme.theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.theme.colors.onSurface }]}>
            {language.currentLanguage === 'ar' ? 'الإعدادات' : 'Settings'}
          </Text>

          <List.Item
            title={language.currentLanguage === 'ar' ? 'الوضع الداكن' : 'Dark Mode'}
            left={(props) => <List.Icon {...props} icon="theme-light-dark" />}
            right={() => (
              <Switch
                value={theme.mode === 'dark'}
                onValueChange={handleThemeToggle}
              />
            )}
          />

          <Divider />

          <List.Item
            title={language.currentLanguage === 'ar' ? 'اللغة العربية' : 'Arabic Language'}
            left={(props) => <List.Icon {...props} icon="translate" />}
            right={() => (
              <Switch
                value={language.currentLanguage === 'ar'}
                onValueChange={handleLanguageToggle}
              />
            )}
          />

          <Divider />

          <List.Item
            title={language.currentLanguage === 'ar' ? 'الإشعارات' : 'Notifications'}
            left={(props) => <List.Icon {...props} icon="bell" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert(t('comingSoon'), 'Notifications settings coming soon')}
          />

          <Divider />

          <List.Item
            title={language.currentLanguage === 'ar' ? 'الخصوصية' : 'Privacy'}
            left={(props) => <List.Icon {...props} icon="shield-account" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert(t('comingSoon'), 'Privacy settings coming soon')}
          />

          <Divider />

          <List.Item
            title={language.currentLanguage === 'ar' ? 'المساعدة' : 'Help & Support'}
            left={(props) => <List.Icon {...props} icon="help-circle" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert(t('comingSoon'), 'Help & Support coming soon')}
          />
        </Surface>

        {/* Action Buttons */}
        <View style={styles.logoutContainer}>
          <Button
            mode="outlined"
            onPress={() => {
              // Reset to auth screens for testing
              dispatch(logoutUser());
            }}
            style={[styles.logoutButton, { borderColor: theme.theme.colors.primary, marginBottom: 10 }]}
            labelStyle={{ color: theme.theme.colors.primary }}
          >
            {language.currentLanguage === 'ar' ? 'اختبار تسجيل الدخول' : 'Test Login Screens'}
          </Button>

          <Button
            mode="outlined"
            onPress={handleLogout}
            style={[styles.logoutButton, { borderColor: theme.theme.colors.error }]}
            labelStyle={{ color: theme.theme.colors.error }}
          >
            {language.currentLanguage === 'ar' ? 'تسجيل الخروج' : 'Logout'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: Platform.OS === 'web' ? 20 : 50,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  profileSection: {
    margin: 15,
    padding: 20,
    borderRadius: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 15,
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  userEmail: {
    fontSize: 14,
    marginBottom: 5,
  },
  verifiedBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    alignSelf: 'flex-start',
  },
  verifiedText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  viewProfileButton: {
    borderRadius: 25,
  },
  statsSection: {
    margin: 15,
    padding: 20,
    borderRadius: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statIcon: {
    fontSize: 24,
    marginBottom: 5,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
  },
  settingsSection: {
    margin: 15,
    borderRadius: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    overflow: 'hidden',
  },
  logoutContainer: {
    padding: 20,
    marginBottom: 20,
  },
  logoutButton: {
    borderRadius: 25,
  },
});

export default NewProfileScreen;
