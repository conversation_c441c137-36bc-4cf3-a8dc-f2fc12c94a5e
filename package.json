{"name": "salestory", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@reduxjs/toolkit": "^2.8.2", "expo": "~53.0.9", "expo-av": "^15.1.4", "expo-camera": "^16.1.6", "expo-file-system": "^18.1.10", "expo-image-picker": "^16.1.4", "expo-localization": "^16.1.5", "expo-media-library": "^17.1.6", "expo-status-bar": "~2.2.3", "i18n-js": "^4.5.1", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "^2.25.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-redux": "^9.2.0", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}