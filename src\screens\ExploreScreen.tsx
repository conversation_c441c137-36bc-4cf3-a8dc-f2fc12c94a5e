import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import {
  fetchStoriesStart,
  fetchStoriesSuccess,
  fetchStoriesFailure,
  setCurrentStory,
  Story,
} from '../store/slices/storySlice';
import ProductCard from '../components/ProductCard';
import FilterBar from '../components/FilterBar';
import { useTranslation } from '../i18n';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const { width } = Dimensions.get('window');

// Reusing the same mock data from HomeScreen
const mockStories: Story[] = [
  {
    id: '1',
    userId: 'user1',
    username: 'fashionista',
    userAvatar: 'https://randomuser.me/api/portraits/women/43.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1539109136881-3be0616acf4b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod1',
      name: 'Summer Dress Collection',
      price: 59.99,
      currency: 'USD',
      description: 'Beautiful summer dress perfect for beach days',
      category: 'fashion',
    },
    likes: 1245,
    views: 5678,
    shares: 123,
    saved: false,
    liked: false,
    createdAt: new Date().toISOString(),
    location: 'New York',
    tags: ['fashion', 'summer', 'dress'],
  },
  {
    id: '2',
    userId: 'user2',
    username: 'techgadgets',
    userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-**********-7041f2a55e12?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod2',
      name: 'Smart Watch Pro',
      price: 199.99,
      currency: 'USD',
      description: 'Latest smartwatch with health tracking features',
      category: 'electronics',
    },
    likes: 876,
    views: 3421,
    shares: 76,
    saved: false,
    liked: false,
    createdAt: new Date().toISOString(),
    location: 'San Francisco',
    tags: ['tech', 'gadgets', 'smartwatch'],
  },
  {
    id: '3',
    userId: 'user3',
    username: 'homedesign',
    userAvatar: 'https://randomuser.me/api/portraits/women/65.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod3',
      name: 'Modern Coffee Table',
      price: 149.99,
      currency: 'USD',
      description: 'Elegant coffee table for your living room',
      category: 'home',
    },
    likes: 543,
    views: 2198,
    shares: 45,
    saved: false,
    liked: false,
    createdAt: new Date().toISOString(),
    location: 'Los Angeles',
    tags: ['furniture', 'home', 'design'],
  },
  {
    id: '4',
    userId: 'user4',
    username: 'beautyguru',
    userAvatar: 'https://randomuser.me/api/portraits/women/22.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1522338242992-e1a54906a8da?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod4',
      name: 'Luxury Skincare Set',
      price: 89.99,
      currency: 'USD',
      description: 'Complete skincare routine for glowing skin',
      category: 'beauty',
    },
    likes: 1876,
    views: 7654,
    shares: 321,
    saved: false,
    liked: false,
    createdAt: new Date().toISOString(),
    location: 'Miami',
    tags: ['beauty', 'skincare', 'luxury'],
  },
  {
    id: '5',
    userId: 'user5',
    username: 'sportsfan',
    userAvatar: 'https://randomuser.me/api/portraits/men/45.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1584735935682-2f2b69dff9d2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod5',
      name: 'Premium Running Shoes',
      price: 129.99,
      currency: 'USD',
      description: 'Lightweight running shoes for maximum performance',
      category: 'sports',
    },
    likes: 765,
    views: 3456,
    shares: 87,
    saved: false,
    liked: false,
    createdAt: new Date().toISOString(),
    location: 'Chicago',
    tags: ['sports', 'running', 'fitness'],
  },
  {
    id: '6',
    userId: 'user6',
    username: 'toycollector',
    userAvatar: 'https://randomuser.me/api/portraits/men/67.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod6',
      name: 'Collectible Action Figure',
      price: 49.99,
      currency: 'USD',
      description: 'Limited edition collectible action figure',
      category: 'toys',
    },
    likes: 432,
    views: 2109,
    shares: 54,
    saved: false,
    liked: false,
    createdAt: new Date().toISOString(),
    location: 'Seattle',
    tags: ['toys', 'collectibles', 'action figures'],
  },
];

const ExploreScreen = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const { stories, loading, error, filters } = useSelector((state: RootState) => state.stories as { stories: Story[]; loading: boolean; error: string | null; filters: any });
  
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch stories on component mount
  useEffect(() => {
    fetchStories();
  }, []);

  // Re-fetch stories when filters change
  useEffect(() => {
    fetchStories();
  }, [filters]);

  const fetchStories = () => {
    dispatch(fetchStoriesStart());
    
    // Simulate API call with timeout
    setTimeout(() => {
      try {
        // Filter stories based on current filters
        let filteredStories = [...mockStories];
        
        if (filters.category) {
          filteredStories = filteredStories.filter(
            (story) => story.product.category === filters.category
          );
        }
        
        if (filters.priceRange) {
          filteredStories = filteredStories.filter(
            (story) =>
              story.product.price >= filters.priceRange![0] &&
              story.product.price <= filters.priceRange![1]
          );
        }
        
        if (filters.location) {
          filteredStories = filteredStories.filter(
            (story) => story.location?.includes(filters.location!)
          );
        }
        
        // Filter by search query if present
        if (searchQuery) {
          const query = searchQuery.toLowerCase();
          filteredStories = filteredStories.filter(
            (story) =>
              story.product.name.toLowerCase().includes(query) ||
              story.product.description.toLowerCase().includes(query) ||
              story.username.toLowerCase().includes(query) ||
              (story.tags && story.tags.some((tag) => tag.toLowerCase().includes(query)))
          );
        }
        
        dispatch(fetchStoriesSuccess(filteredStories));
      } catch (err) {
        dispatch(fetchStoriesFailure('Failed to fetch stories'));
      }
    }, 1000);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // Debounce search to avoid too many API calls
    const timeoutId = setTimeout(() => {
      fetchStories();
    }, 500);
    
    return () => clearTimeout(timeoutId);
  };

  const handleProductPress = (story: Story) => {
    dispatch(setCurrentStory(story));
    // Navigate to story detail or switch to home tab and show this story
    navigation.navigate('Home' as never);
  };

  const toggleViewMode = () => {
    setViewMode(viewMode === 'grid' ? 'list' : 'grid');
  };

  const renderProductCard = ({ item }: { item: Story }) => (
    <ProductCard story={item} onPress={() => handleProductPress(item)} />
  );

  if (loading && stories.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6200ee" />
        <Text style={styles.loadingText}>{t('loading')}</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <Text style={styles.retryText} onPress={fetchStories}>
          {t('tryAgain')}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <FilterBar showSearch={true} onSearch={handleSearch} />
        <TouchableOpacity style={styles.viewModeButton} onPress={toggleViewMode}>
          <Ionicons
            name={viewMode === 'grid' ? 'list-outline' : 'grid-outline'}
            size={24}
            color="#6200ee"
          />
        </TouchableOpacity>
      </View>
      
      {stories.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>{t('noResults')}</Text>
        </View>
      ) : (
        <FlatList
          data={stories}
          renderItem={renderProductCard}
          keyExtractor={(item) => item.id}
          numColumns={viewMode === 'grid' ? 2 : 1}
          key={viewMode} // Force re-render when view mode changes
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingRight: 15,
  },
  viewModeButton: {
    padding: 8,
  },
  listContainer: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff3b30',
    textAlign: 'center',
    marginBottom: 10,
  },
  retryText: {
    fontSize: 16,
    color: '#6200ee',
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  },
});

export default ExploreScreen;