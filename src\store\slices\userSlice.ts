import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UserState {
  id: string | null;
  username: string | null;
  email: string | null;
  profilePicture: string | null;
  coverPhoto: string | null;
  isAuthenticated: boolean;
  subscription: 'basic' | 'pro' | 'business' | null;
  stats: {
    sales: number;
    views: number;
    ratings: number;
  };
  isVerified: boolean;
}

const initialState: UserState = {
  id: null,
  username: null,
  email: null,
  profilePicture: null,
  coverPhoto: null,
  isAuthenticated: false,
  subscription: null,
  stats: {
    sales: 0,
    views: 0,
    ratings: 0,
  },
  isVerified: false,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<Partial<UserState>>) => {
      return { ...state, ...action.payload };
    },
    login: (state, action: PayloadAction<{ id: string; username: string; email: string }>) => {
      state.id = action.payload.id;
      state.username = action.payload.username;
      state.email = action.payload.email;
      state.isAuthenticated = true;
    },
    logout: (state) => {
      return initialState;
    },
    updateProfile: (state, action: PayloadAction<Partial<UserState>>) => {
      return { ...state, ...action.payload };
    },
    updateStats: (state, action: PayloadAction<Partial<UserState['stats']>>) => {
      state.stats = { ...state.stats, ...action.payload };
    },
    setSubscription: (state, action: PayloadAction<UserState['subscription']>) => {
      state.subscription = action.payload;
    },
    setVerificationStatus: (state, action: PayloadAction<boolean>) => {
      state.isVerified = action.payload;
    },
  },
});

export const {
  setUser,
  login,
  logout,
  updateProfile,
  updateStats,
  setSubscription,
  setVerificationStatus,
} = userSlice.actions;

export default userSlice.reducer;