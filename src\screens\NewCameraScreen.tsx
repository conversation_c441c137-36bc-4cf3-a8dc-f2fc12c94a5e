import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Platform,
  Alert,
} from 'react-native';
import {
  Button,
  TextInput,
  Surface,
  IconButton,
  Chip,
} from 'react-native-paper';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { useTranslation } from '../i18n';

const NewCameraScreen = () => {
  const theme = useSelector((state: RootState) => state.theme);
  const language = useSelector((state: RootState) => state.language);
  const { t } = useTranslation();

  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [productName, setProductName] = useState('');
  const [productDescription, setProductDescription] = useState('');
  const [productPrice, setProductPrice] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  const categories = [
    { id: '1', name: 'Fashion', nameAr: 'أزياء' },
    { id: '2', name: 'Electronics', nameAr: 'إلكترونيات' },
    { id: '3', name: 'Home', nameAr: 'منزل' },
    { id: '4', name: 'Beauty', nameAr: 'جمال' },
    { id: '5', name: 'Sports', nameAr: 'رياضة' },
  ];

  const handleImagePicker = () => {
    Alert.alert(
      language.currentLanguage === 'ar' ? 'اختر صورة' : 'Select Image',
      language.currentLanguage === 'ar' ? 'اختر مصدر الصورة' : 'Choose image source',
      [
        {
          text: language.currentLanguage === 'ar' ? 'الكاميرا' : 'Camera',
          onPress: () => {
            // Mock adding camera image
            const mockImage = 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300';
            setSelectedImages(prev => [...prev, mockImage]);
          }
        },
        {
          text: language.currentLanguage === 'ar' ? 'المعرض' : 'Gallery',
          onPress: () => {
            // Mock adding gallery image
            const mockImage = 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=300';
            setSelectedImages(prev => [...prev, mockImage]);
          }
        },
        {
          text: t('cancel'),
          style: 'cancel'
        }
      ]
    );
  };

  const handlePublish = () => {
    if (!productName || !productDescription || !productPrice || !selectedCategory) {
      Alert.alert(t('error'), t('fillAllFields'));
      return;
    }

    Alert.alert(
      t('success'),
      language.currentLanguage === 'ar' ? 'تم نشر المنتج بنجاح!' : 'Product published successfully!',
      [
        {
          text: t('ok'),
          onPress: () => {
            // Reset form
            setProductName('');
            setProductDescription('');
            setProductPrice('');
            setSelectedCategory('');
            setSelectedImages([]);
          }
        }
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.theme.colors.background }]}>
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.theme.colors.surface }]}>
        <Text style={[styles.headerTitle, { color: theme.theme.colors.onSurface }]}>
          {language.currentLanguage === 'ar' ? 'إنشاء قصة منتج' : 'Create Product Story'}
        </Text>
      </Surface>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Image Selection */}
        <Surface style={[styles.section, { backgroundColor: theme.theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.theme.colors.onSurface }]}>
            {language.currentLanguage === 'ar' ? 'صور المنتج' : 'Product Images'}
          </Text>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity style={styles.addImageButton} onPress={handleImagePicker}>
              <IconButton icon="camera-plus" size={40} iconColor={theme.theme.colors.primary} />
              <Text style={[styles.addImageText, { color: theme.theme.colors.primary }]}>
                {language.currentLanguage === 'ar' ? 'إضافة صورة' : 'Add Image'}
              </Text>
            </TouchableOpacity>
            
            {selectedImages.map((image, index) => (
              <View key={index} style={styles.imageContainer}>
                <Image source={{ uri: image }} style={styles.selectedImage} />
                <IconButton
                  icon="close-circle"
                  size={20}
                  style={styles.removeImageButton}
                  iconColor={theme.theme.colors.error}
                  onPress={() => setSelectedImages(prev => prev.filter((_, i) => i !== index))}
                />
              </View>
            ))}
          </ScrollView>
        </Surface>

        {/* Product Details */}
        <Surface style={[styles.section, { backgroundColor: theme.theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.theme.colors.onSurface }]}>
            {language.currentLanguage === 'ar' ? 'تفاصيل المنتج' : 'Product Details'}
          </Text>

          <TextInput
            label={language.currentLanguage === 'ar' ? 'اسم المنتج' : 'Product Name'}
            value={productName}
            onChangeText={setProductName}
            mode="outlined"
            style={styles.input}
          />

          <TextInput
            label={language.currentLanguage === 'ar' ? 'وصف المنتج' : 'Product Description'}
            value={productDescription}
            onChangeText={setProductDescription}
            mode="outlined"
            multiline
            numberOfLines={4}
            style={styles.input}
          />

          <TextInput
            label={language.currentLanguage === 'ar' ? 'السعر' : 'Price ($)'}
            value={productPrice}
            onChangeText={setProductPrice}
            mode="outlined"
            keyboardType="numeric"
            style={styles.input}
          />
        </Surface>

        {/* Category Selection */}
        <Surface style={[styles.section, { backgroundColor: theme.theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.theme.colors.onSurface }]}>
            {language.currentLanguage === 'ar' ? 'الفئة' : 'Category'}
          </Text>
          
          <View style={styles.categoriesContainer}>
            {categories.map((category) => (
              <Chip
                key={category.id}
                selected={selectedCategory === category.name}
                onPress={() => setSelectedCategory(
                  selectedCategory === category.name ? '' : category.name
                )}
                style={styles.categoryChip}
              >
                {language.currentLanguage === 'ar' ? category.nameAr : category.name}
              </Chip>
            ))}
          </View>
        </Surface>

        {/* Publish Button */}
        <View style={styles.publishContainer}>
          <Button
            mode="contained"
            onPress={handlePublish}
            style={styles.publishButton}
            contentStyle={styles.publishButtonContent}
          >
            {language.currentLanguage === 'ar' ? 'نشر المنتج' : 'Publish Product'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: Platform.OS === 'web' ? 20 : 50,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  section: {
    margin: 15,
    padding: 20,
    borderRadius: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  addImageButton: {
    width: 120,
    height: 120,
    borderRadius: 15,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: '#ccc',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  addImageText: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 5,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 10,
  },
  selectedImage: {
    width: 120,
    height: 120,
    borderRadius: 15,
  },
  removeImageButton: {
    position: 'absolute',
    top: -10,
    right: -10,
  },
  input: {
    marginBottom: 15,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  categoryChip: {
    marginBottom: 5,
  },
  publishContainer: {
    padding: 20,
  },
  publishButton: {
    borderRadius: 25,
  },
  publishButtonContent: {
    height: 50,
  },
});

export default NewCameraScreen;
