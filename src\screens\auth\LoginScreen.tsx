import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {
  TextInput,
  Button,
  Surface,
  IconButton,
  Divider,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { loginStart, loginUser, loginFailure } from '../../store/slices/userSlice';
import { useTranslation } from '../../i18n';

const LoginScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const theme = useSelector((state: RootState) => state.theme);
  const { loading } = useSelector((state: RootState) => state.user);
  const { t } = useTranslation();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert(t('error'), t('fillAllFields'));
      return;
    }

    try {
      dispatch(loginStart());

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Simulate login API call
      const userData = {
        id: '1',
        email,
        name: 'John Doe',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
        isVerified: true,
      };

      dispatch(loginUser(userData));
    } catch (error) {
      dispatch(loginFailure(t('loginFailed')));
      Alert.alert(t('error'), t('loginFailed'));
    }
  };

  const handleSocialLogin = (provider: string) => {
    Alert.alert(t('comingSoon'), `${provider} ${t('loginComingSoon')}`);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={() => navigation.goBack()}
            iconColor={theme.theme.colors.onSurface}
          />
          <Text style={[styles.title, { color: theme.theme.colors.onSurface }]}>
            {t('signIn')}
          </Text>
          <View style={{ width: 40 }} />
        </View>

        {/* Login Form */}
        <Surface style={[styles.formCard, { backgroundColor: theme.theme.colors.surface }]}>
          <Text style={[styles.welcomeText, { color: theme.theme.colors.onSurface }]}>
            {t('welcomeBack')}
          </Text>
          <Text style={[styles.subtitleText, { color: theme.theme.colors.onSurfaceVariant }]}>
            {t('signInToContinue')}
          </Text>

          <TextInput
            label={t('email')}
            value={email}
            onChangeText={setEmail}
            mode="outlined"
            keyboardType="email-address"
            autoCapitalize="none"
            style={styles.input}
            left={<TextInput.Icon icon="email" />}
          />

          <TextInput
            label={t('password')}
            value={password}
            onChangeText={setPassword}
            mode="outlined"
            secureTextEntry={!showPassword}
            style={styles.input}
            left={<TextInput.Icon icon="lock" />}
            right={
              <TextInput.Icon
                icon={showPassword ? 'eye-off' : 'eye'}
                onPress={() => setShowPassword(!showPassword)}
              />
            }
          />

          <Button
            mode="text"
            onPress={() => Alert.alert(t('comingSoon'), t('forgotPasswordComingSoon'))}
            style={styles.forgotButton}
          >
            {t('forgotPassword')}
          </Button>

          <Button
            mode="contained"
            onPress={handleLogin}
            loading={loading}
            disabled={loading}
            style={styles.loginButton}
            contentStyle={styles.buttonContent}
          >
            {t('signIn')}
          </Button>

          <Divider style={styles.divider} />

          {/* Social Login */}
          <Text style={[styles.socialText, { color: theme.theme.colors.onSurfaceVariant }]}>
            {t('orContinueWith')}
          </Text>

          <View style={styles.socialButtons}>
            <Button
              mode="outlined"
              onPress={() => handleSocialLogin('Google')}
              style={styles.socialButton}
              icon="google"
            >
              Google
            </Button>
            <Button
              mode="outlined"
              onPress={() => handleSocialLogin('Facebook')}
              style={styles.socialButton}
              icon="facebook"
            >
              Facebook
            </Button>
          </View>

          {/* Sign Up Link */}
          <View style={styles.signUpContainer}>
            <Text style={[styles.signUpText, { color: theme.theme.colors.onSurfaceVariant }]}>
              {t('dontHaveAccount')}
            </Text>
            <Button
              mode="text"
              onPress={() => navigation.navigate('Register' as never)}
              compact
            >
              {t('signUp')}
            </Button>
          </View>
        </Surface>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 30,
    marginTop: Platform.OS === 'web' ? 20 : 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  formCard: {
    borderRadius: 20,
    padding: 30,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  welcomeText: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitleText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
  },
  input: {
    marginBottom: 15,
  },
  forgotButton: {
    alignSelf: 'flex-end',
    marginBottom: 20,
  },
  loginButton: {
    marginBottom: 20,
    borderRadius: 25,
  },
  buttonContent: {
    height: 50,
  },
  divider: {
    marginVertical: 20,
  },
  socialText: {
    textAlign: 'center',
    marginBottom: 15,
  },
  socialButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  socialButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  signUpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signUpText: {
    fontSize: 14,
  },
});

export default LoginScreen;
