import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Platform,
} from 'react-native';
import {
  Surface,
  IconButton,
  Button,
} from 'react-native-paper';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { useTranslation } from '../i18n';
import { useNavigation } from '@react-navigation/native';

const CreateStoryScreen = () => {
  const theme = useSelector((state: RootState) => state.theme);
  const language = useSelector((state: RootState) => state.language);
  const { t } = useTranslation();
  const navigation = useNavigation();

  return (
    <View style={[styles.container, { backgroundColor: theme.theme.colors.background }]}>
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.theme.colors.surface }]}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => navigation.goBack()}
          iconColor={theme.theme.colors.onSurface}
        />
        <Text style={[styles.headerTitle, { color: theme.theme.colors.onSurface }]}>
          {language.currentLanguage === 'ar' ? 'إنشاء قصة' : 'Create Story'}
        </Text>
        <View style={{ width: 40 }} />
      </Surface>

      {/* Content */}
      <View style={styles.content}>
        <Text style={[styles.comingSoonIcon, { color: theme.theme.colors.primary }]}>
          🎬
        </Text>
        <Text style={[styles.comingSoonTitle, { color: theme.theme.colors.onSurface }]}>
          {t('comingSoon')}
        </Text>
        <Text style={[styles.comingSoonText, { color: theme.theme.colors.onSurfaceVariant }]}>
          {language.currentLanguage === 'ar' 
            ? 'ميزة إنشاء القصص قريباً! ستتمكن من إنشاء قصص تفاعلية لمنتجاتك.'
            : 'Story creation feature coming soon! You\'ll be able to create interactive stories for your products.'
          }
        </Text>
        
        <Button
          mode="contained"
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          {language.currentLanguage === 'ar' ? 'العودة' : 'Go Back'}
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    paddingTop: Platform.OS === 'web' ? 10 : 40,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  comingSoonIcon: {
    fontSize: 80,
    marginBottom: 20,
  },
  comingSoonTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  comingSoonText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  backButton: {
    borderRadius: 25,
    paddingHorizontal: 30,
  },
});

export default CreateStoryScreen;
