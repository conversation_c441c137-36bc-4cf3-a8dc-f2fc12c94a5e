import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  Switch,
  Modal,
  TextInput,
  Alert,
  FlatList,
  Platform,
} from 'react-native';
import { Ionicons, MaterialIcons, FontAwesome5 } from '@expo/vector-icons';
import { useTranslation } from '../i18n';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { setThemeMode } from '../store/slices/themeSlice';
import { setLanguage } from '../store/slices/languageSlice';
import { useNavigation } from '@react-navigation/native';
import { Story } from '../store/slices/storySlice';
import setUserData from '../store/slices/userSlice';
import { logout } from '../store/slices/userSlice';

// Define user profile interface
interface UserProfile {
  id: string;
  username: string;
  fullName: string;
  email: string;
  phone: string;
  avatar: string;
  coverPhoto: string;
  bio: string;
  location: string;
  website: string;
  joinDate: string;
  isVerified: boolean;
  subscriptionPlan: 'basic' | 'pro' | 'business' | null;
  subscriptionEndDate: string | null;
  stats: {
    followers: number;
    following: number;
    stories: number;
    sales: number;
    views: number;
    rating: number;
  };
  socialLinks: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
    tiktok?: string;
  };
  preferences: {
    darkMode: boolean;
    notifications: boolean;
    language: string;
    currency: string;
    privacySettings: {
      profileVisibility: 'public' | 'private';
      activityStatus: boolean;
      readReceipts: boolean;
    };
  };
}

// Mock user data
const mockUserProfile: UserProfile = {
  id: 'user123',
  username: 'ahmed_seller',
  fullName: 'Ahmed Mohammed',
  email: '<EMAIL>',
  phone: '+966 50 123 4567',
  avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
  coverPhoto: 'https://images.unsplash.com/photo-1557683316-973673baf926?ixlib=rb-1.2.1&auto=format&fit=crop&w=1080&q=80',
  bio: 'Passionate seller of premium products. Quality is my priority!',
  location: 'Riyadh, Saudi Arabia',
  website: 'www.ahmedstore.com',
  joinDate: '2022-05-15',
  isVerified: true,
  subscriptionPlan: 'pro',
  subscriptionEndDate: '2023-12-31',
  stats: {
    followers: 1245,
    following: 356,
    stories: 87,
    sales: 142,
    views: 15678,
    rating: 4.8,
  },
  socialLinks: {
    instagram: 'ahmed_seller',
    facebook: 'ahmedsellerofficial',
    twitter: 'ahmed_seller',
    tiktok: 'ahmed_seller',
  },
  preferences: {
    darkMode: false,
    notifications: true,
    language: 'ar',
    currency: 'SAR',
    privacySettings: {
      profileVisibility: 'public',
      activityStatus: true,
      readReceipts: true,
    },
  },
};

// Mock stories data (reusing from other screens)
const mockStories: Story[] = [
  {
    id: '1',
    userId: 'user123',
    username: 'ahmed_seller',
    userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-**********-7041f2a55e12?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod1',
      name: 'Smart Watch Pro',
      price: 199.99,
      currency: 'USD',
      description: 'Latest smartwatch with health tracking features',
      category: 'electronics',
    },
    likes: 876,
    views: 3421,
    shares: 76,
    saved: false,
    liked: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(), // 2 days ago
    location: 'Riyadh',
    tags: ['tech', 'gadgets', 'smartwatch'],
  },
  {
    id: '2',
    userId: 'user123',
    username: 'ahmed_seller',
    userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1585565804112-f201f68c48b4?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod2',
      name: 'Wireless Headphones',
      price: 149.99,
      currency: 'USD',
      description: 'Premium noise-cancelling wireless headphones',
      category: 'electronics',
    },
    likes: 543,
    views: 2198,
    shares: 45,
    saved: false,
    liked: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(), // 5 days ago
    location: 'Riyadh',
    tags: ['audio', 'music', 'headphones'],
  },
  {
    id: '3',
    userId: 'user123',
    username: 'ahmed_seller',
    userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    media: [
      {
        type: 'image',
        url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
      },
    ],
    product: {
      id: 'prod3',
      name: 'Portable Speaker',
      price: 79.99,
      currency: 'USD',
      description: 'Waterproof portable Bluetooth speaker',
      category: 'electronics',
    },
    likes: 321,
    views: 1543,
    shares: 32,
    saved: false,
    liked: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10).toISOString(), // 10 days ago
    location: 'Riyadh',
    tags: ['audio', 'speaker', 'portable'],
  },
];

// Define subscription plans
const subscriptionPlans = [
  {
    id: 'basic',
    name: 'Basic',
    price: 9.99,
    currency: 'USD',
    features: [
      'Limited stories (10/month)',
      'Basic filters',
      'Standard support',
      'No ads',
    ],
  },
  {
    id: 'pro',
    name: 'Pro',
    price: 19.99,
    currency: 'USD',
    features: [
      'Unlimited stories',
      'Advanced filters',
      'Priority in feed',
      'Priority support',
      'No ads',
    ],
  },
  {
    id: 'business',
    name: 'Business',
    price: 39.99,
    currency: 'USD',
    features: [
      'Unlimited stories',
      'All filters',
      'Top priority in feed',
      'Advanced analytics',
      'Dedicated support',
      'No ads',
      'Custom branding',
    ],
  },
];

const ProfileScreen = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const theme = useSelector((state: RootState) => state.theme);
  const language = useSelector((state: RootState) => state.language);
  
  // State for user profile
  const [userProfile, setUserProfile] = useState<UserProfile>(mockUserProfile);
  const [activeTab, setActiveTab] = useState<'stories' | 'saved' | 'stats'>('stories');
  const [isEditProfileModalVisible, setIsEditProfileModalVisible] = useState(false);
  const [isSubscriptionModalVisible, setIsSubscriptionModalVisible] = useState(false);
  const [isSettingsModalVisible, setIsSettingsModalVisible] = useState(false);
  const [editedProfile, setEditedProfile] = useState<Partial<UserProfile>>({
    fullName: userProfile.fullName,
    bio: userProfile.bio,
    location: userProfile.location,
    website: userProfile.website,
  });
  
  // Toggle dark mode
  const toggleDarkMode = () => {
    const newMode = userProfile.preferences.darkMode ? 'light' : 'dark';
    dispatch(setThemeMode(newMode));
    setUserProfile({
      ...userProfile,
      preferences: {
        ...userProfile.preferences,
        darkMode: !userProfile.preferences.darkMode,
      },
    });
  };
  
  // Change language
  const changeLanguage = (lang: string) => {
    dispatch(setLanguage(lang as 'en' | 'ar')); // Type assertion to supported language
    setUserProfile({
      ...userProfile,
      preferences: {
        ...userProfile.preferences,
        language: lang,
      },
    });
  };
  
  // Toggle notifications
  const toggleNotifications = () => {
    setUserProfile({
      ...userProfile,
      preferences: {
        ...userProfile.preferences,
        notifications: !userProfile.preferences.notifications,
      },
    });
  };
  
  // Update profile
  const updateProfile = () => {
    if (!editedProfile.fullName?.trim()) {
      Alert.alert(t('error'), t('nameRequired'));
      return;
    }
    
    const updatedProfile = {
      ...userProfile,
      ...editedProfile,
    };
    
    setUserProfile(updatedProfile);
    dispatch(setUserData({ type: 'update', payload: updatedProfile }));
    setIsEditProfileModalVisible(false);
    
    Alert.alert(t('success'), t('profileUpdated'));
  };
  
  // Subscribe to plan
  const subscribeToPlan = (planId: string) => {
    // In a real app, this would navigate to a payment screen
    Alert.alert(
      t('subscriptionConfirmation'),
      t('subscriptionConfirmationMessage', { plan: planId }),
      [
        { text: t('cancel'), style: 'cancel' },
        {
          text: t('subscribe'),
          onPress: () => {
            // Simulate successful subscription
            const endDate = new Date();
            endDate.setFullYear(endDate.getFullYear() + 1);
            
            setUserProfile({
              ...userProfile,
              subscriptionPlan: planId as 'basic' | 'pro' | 'business',
              subscriptionEndDate: endDate.toISOString(),
            });
            
            setIsSubscriptionModalVisible(false);
            Alert.alert(t('success'), t('subscriptionSuccess'));
          },
        },
      ]
    );
  };
  
  // Handle logout
  const handleLogout = () => {
    Alert.alert(
      t('logoutConfirmation'),
      t('logoutConfirmationMessage'),
      [
        { text: t('cancel'), style: 'cancel' },
        {
          text: t('logout'),
          style: 'destructive',
          onPress: () => {
            dispatch(logout());
            // In a real app, this would navigate to the login screen
            Alert.alert(t('loggedOut'), t('loggedOutSuccess'));
          },
        },
      ]
    );
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString((language as {currentLanguage: string}).currentLanguage, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };
  
  // Render story item
  const renderStoryItem = ({ item }: { item: Story }) => (
    <TouchableOpacity
      style={styles.storyItem}
      onPress={() => {
        // Navigate to story detail
      }}
    >
      <Image source={{ uri: item.media[0].url }} style={styles.storyImage} />
      <View style={styles.storyInfo}>
        <Text style={styles.storyTitle} numberOfLines={1}>
          {item.product.name}
        </Text>
        <Text style={styles.storyPrice}>
          {item.product.price.toFixed(2)} {item.product.currency}
        </Text>
        <View style={styles.storyStats}>
          <View style={styles.statItem}>
            <Ionicons name="heart" size={14} color="#FF5252" />
            <Text style={styles.statText}>{item.likes}</Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="eye" size={14} color="#666" />
            <Text style={styles.statText}>{item.views}</Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="share-social" size={14} color="#666" />
            <Text style={styles.statText}>{item.shares}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
  
  // Render subscription plan
  const renderSubscriptionPlan = (plan: typeof subscriptionPlans[0]) => (
    <View
      style={[
        styles.planCard,
        userProfile.subscriptionPlan === plan.id && styles.activePlanCard,
      ]}
      key={plan.id}
    >
      <View style={styles.planHeader}>
        <Text style={styles.planName}>{plan.name}</Text>
        <Text style={styles.planPrice}>
          {plan.price} {plan.currency}/{t('month')}
        </Text>
      </View>
      
      <View style={styles.planFeatures}>
        {plan.features.map((feature, index) => (
          <View style={styles.featureItem} key={index}>
            <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
            <Text style={styles.featureText}>{t(feature)}</Text>
          </View>
        ))}
      </View>
      
      {userProfile.subscriptionPlan === plan.id ? (
        <View style={styles.currentPlanButton}>
          <Text style={styles.currentPlanText}>{t('currentPlan')}</Text>
          <Text style={styles.expiryText}>
            {t('expiresOn', {
              date: userProfile.subscriptionEndDate
                ? formatDate(userProfile.subscriptionEndDate)
                : '',
            })}
          </Text>
        </View>
      ) : (
        <TouchableOpacity
          style={styles.subscribePlanButton}
          onPress={() => subscribeToPlan(plan.id)}
        >
          <Text style={styles.subscribePlanText}>{t('subscribe')}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
  
  // Render edit profile modal
  const renderEditProfileModal = () => (
    <Modal
      visible={isEditProfileModalVisible}
      transparent
      animationType="slide"
      onRequestClose={() => setIsEditProfileModalVisible(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>{t('editProfile')}</Text>
          
          <ScrollView showsVerticalScrollIndicator={false}>
            <Text style={styles.inputLabel}>{t('fullName')}</Text>
            <TextInput
              style={styles.input}
              value={editedProfile.fullName}
              onChangeText={(text) =>
                setEditedProfile({ ...editedProfile, fullName: text })
              }
              placeholder={t('enterFullName')}
              placeholderTextColor="#999"
            />
            
            <Text style={styles.inputLabel}>{t('bio')}</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={editedProfile.bio}
              onChangeText={(text) =>
                setEditedProfile({ ...editedProfile, bio: text })
              }
              placeholder={t('enterBio')}
              placeholderTextColor="#999"
              multiline
            />
            
            <Text style={styles.inputLabel}>{t('location')}</Text>
            <TextInput
              style={styles.input}
              value={editedProfile.location}
              onChangeText={(text) =>
                setEditedProfile({ ...editedProfile, location: text })
              }
              placeholder={t('enterLocation')}
              placeholderTextColor="#999"
            />
            
            <Text style={styles.inputLabel}>{t('website')}</Text>
            <TextInput
              style={styles.input}
              value={editedProfile.website}
              onChangeText={(text) =>
                setEditedProfile({ ...editedProfile, website: text })
              }
              placeholder={t('enterWebsite')}
              placeholderTextColor="#999"
              keyboardType="url"
            />
          </ScrollView>
          
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => setIsEditProfileModalVisible(false)}
            >
              <Text style={styles.modalButtonText}>{t('cancel')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.modalButton, styles.primaryButton]}
              onPress={updateProfile}
            >
              <Text style={styles.primaryButtonText}>{t('save')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
  
  // Render subscription modal
  const renderSubscriptionModal = () => (
    <Modal
      visible={isSubscriptionModalVisible}
      transparent
      animationType="slide"
      onRequestClose={() => setIsSubscriptionModalVisible(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>{t('subscriptionPlans')}</Text>
          
          <ScrollView showsVerticalScrollIndicator={false}>
            {subscriptionPlans.map(renderSubscriptionPlan)}
          </ScrollView>
          
          <TouchableOpacity
            style={[styles.modalButton, styles.closeButton]}
            onPress={() => setIsSubscriptionModalVisible(false)}
          >
            <Text style={styles.closeButtonText}>{t('close')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
  
  // Render settings modal
  const renderSettingsModal = () => (
    <Modal
      visible={isSettingsModalVisible}
      transparent
      animationType="slide"
      onRequestClose={() => setIsSettingsModalVisible(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>{t('settings')}</Text>
          
          <ScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.settingSection}>
              <Text style={styles.settingSectionTitle}>{t('appearance')}</Text>
              
              <View style={styles.settingItem}>
                <View style={styles.settingLabelContainer}>
                  <Ionicons name="moon" size={20} color="#666" />
                  <Text style={styles.settingLabel}>{t('darkMode')}</Text>
                </View>
                <Switch
                  value={userProfile.preferences.darkMode}
                  onValueChange={toggleDarkMode}
                  trackColor={{ false: '#ccc', true: '#6200ee' }}
                  thumbColor={Platform.OS === 'ios' ? '#fff' : '#f4f3f4'}
                />
              </View>
            </View>
            
            <View style={styles.settingSection}>
              <Text style={styles.settingSectionTitle}>{t('language')}</Text>
              
              <View style={styles.languageOptions}>
                <TouchableOpacity
                  style={[
                    styles.languageOption,
                    userProfile.preferences.language === 'ar' &&
                      styles.activeLanguageOption,
                  ]}
                  onPress={() => changeLanguage('ar')}
                >
                  <Text
                    style={[
                      styles.languageText,
                      userProfile.preferences.language === 'ar' &&
                        styles.activeLanguageText,
                    ]}
                  >
                    العربية
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[
                    styles.languageOption,
                    userProfile.preferences.language === 'en' &&
                      styles.activeLanguageOption,
                  ]}
                  onPress={() => changeLanguage('en')}
                >
                  <Text
                    style={[
                      styles.languageText,
                      userProfile.preferences.language === 'en' &&
                        styles.activeLanguageText,
                    ]}
                  >
                    English
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            
            <View style={styles.settingSection}>
              <Text style={styles.settingSectionTitle}>{t('notifications')}</Text>
              
              <View style={styles.settingItem}>
                <View style={styles.settingLabelContainer}>
                  <Ionicons name="notifications" size={20} color="#666" />
                  <Text style={styles.settingLabel}>{t('pushNotifications')}</Text>
                </View>
                <Switch
                  value={userProfile.preferences.notifications}
                  onValueChange={toggleNotifications}
                  trackColor={{ false: '#ccc', true: '#6200ee' }}
                  thumbColor={Platform.OS === 'ios' ? '#fff' : '#f4f3f4'}
                />
              </View>
            </View>
            
            <View style={styles.settingSection}>
              <Text style={styles.settingSectionTitle}>{t('privacy')}</Text>
              
              <View style={styles.settingItem}>
                <View style={styles.settingLabelContainer}>
                  <Ionicons name="eye" size={20} color="#666" />
                  <Text style={styles.settingLabel}>{t('profileVisibility')}</Text>
                </View>
                <View style={styles.privacySelector}>
                  <Text style={styles.privacyValue}>
                    {userProfile.preferences.privacySettings.profileVisibility === 'public'
                      ? t('public')
                      : t('private')}
                  </Text>
                  <Ionicons name="chevron-forward" size={16} color="#666" />
                </View>
              </View>
              
              <View style={styles.settingItem}>
                <View style={styles.settingLabelContainer}>
                  <Ionicons name="time" size={20} color="#666" />
                  <Text style={styles.settingLabel}>{t('activityStatus')}</Text>
                </View>
                <Switch
                  value={userProfile.preferences.privacySettings.activityStatus}
                  onValueChange={(value) =>
                    setUserProfile({
                      ...userProfile,
                      preferences: {
                        ...userProfile.preferences,
                        privacySettings: {
                          ...userProfile.preferences.privacySettings,
                          activityStatus: value,
                        },
                      },
                    })
                  }
                  trackColor={{ false: '#ccc', true: '#6200ee' }}
                  thumbColor={Platform.OS === 'ios' ? '#fff' : '#f4f3f4'}
                />
              </View>
            </View>
            
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <Ionicons name="log-out" size={20} color="#F44336" />
              <Text style={styles.logoutText}>{t('logout')}</Text>
            </TouchableOpacity>
          </ScrollView>
          
          <TouchableOpacity
            style={[styles.modalButton, styles.closeButton]}
            onPress={() => setIsSettingsModalVisible(false)}
          >
            <Text style={styles.closeButtonText}>{t('close')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
  
  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Cover Photo and Avatar */}
        <View style={styles.coverContainer}>
          <Image source={{ uri: userProfile.coverPhoto }} style={styles.coverPhoto} />
          <View style={styles.avatarContainer}>
            <Image source={{ uri: userProfile.avatar }} style={styles.avatar} />
            {userProfile.isVerified && (
              <View style={styles.verifiedBadge}>
                <Ionicons name="checkmark-circle" size={20} color="#FFFFFF" />
              </View>
            )}
          </View>
        </View>
        
        {/* Profile Info */}
        <View style={styles.profileInfo}>
          <View style={styles.nameContainer}>
            <Text style={styles.fullName}>{userProfile.fullName}</Text>
            <Text style={styles.username}>@{userProfile.username}</Text>
          </View>
          
          <View style={styles.bioContainer}>
            <Text style={styles.bio}>{userProfile.bio}</Text>
          </View>
          
          <View style={styles.detailsContainer}>
            {userProfile.location && (
              <View style={styles.detailItem}>
                <Ionicons name="location" size={16} color="#666" />
                <Text style={styles.detailText}>{userProfile.location}</Text>
              </View>
            )}
            
            {userProfile.website && (
              <View style={styles.detailItem}>
                <Ionicons name="globe" size={16} color="#666" />
                <Text style={styles.detailText}>{userProfile.website}</Text>
              </View>
            )}
            
            <View style={styles.detailItem}>
              <Ionicons name="calendar" size={16} color="#666" />
              <Text style={styles.detailText}>
                {t('joinedOn', { date: formatDate(userProfile.joinDate) })}
              </Text>
            </View>
          </View>
          
          <View style={styles.statsContainer}>
            <View style={styles.statColumn}>
              <Text style={styles.statValue}>{userProfile.stats.followers}</Text>
              <Text style={styles.statLabel}>{t('followers')}</Text>
            </View>
            
            <View style={styles.statColumn}>
              <Text style={styles.statValue}>{userProfile.stats.following}</Text>
              <Text style={styles.statLabel}>{t('following')}</Text>
            </View>
            
            <View style={styles.statColumn}>
              <Text style={styles.statValue}>{userProfile.stats.stories}</Text>
              <Text style={styles.statLabel}>{t('stories')}</Text>
            </View>
            
            <View style={styles.statColumn}>
              <Text style={styles.statValue}>{userProfile.stats.sales}</Text>
              <Text style={styles.statLabel}>{t('sales')}</Text>
            </View>
          </View>
          
          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => setIsEditProfileModalVisible(true)}
            >
              <Text style={styles.actionButtonText}>{t('editProfile')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryButton]}
              onPress={() => setIsSettingsModalVisible(true)}
            >
              <Ionicons name="settings-outline" size={18} color="#666" />
            </TouchableOpacity>
          </View>
          
          {/* Subscription Banner */}
          <TouchableOpacity
            style={styles.subscriptionBanner}
            onPress={() => setIsSubscriptionModalVisible(true)}
          >
            <View style={styles.subscriptionInfo}>
              <Text style={styles.subscriptionTitle}>
                {userProfile.subscriptionPlan
                  ? t('currentPlan', {
                      plan: t(userProfile.subscriptionPlan),
                    })
                  : t('noPlan')}
              </Text>
              <Text style={styles.subscriptionDescription}>
                {userProfile.subscriptionPlan
                  ? t('subscriptionExpiry', {
                      date: userProfile.subscriptionEndDate
                        ? formatDate(userProfile.subscriptionEndDate)
                        : '',
                    })
                  : t('upgradeToUnlock')}
              </Text>
            </View>
            <View style={styles.subscriptionAction}>
              <Text style={styles.subscriptionActionText}>
                {userProfile.subscriptionPlan ? t('manage') : t('upgrade')}
              </Text>
              <Ionicons name="chevron-forward" size={16} color="#6200ee" />
            </View>
          </TouchableOpacity>
        </View>
        
        {/* Content Tabs */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 'stories' && styles.activeTabButton,
            ]}
            onPress={() => setActiveTab('stories')}
          >
            <Text
              style={[
                styles.tabButtonText,
                activeTab === 'stories' && styles.activeTabButtonText,
              ]}
            >
              {t('myStories')}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 'saved' && styles.activeTabButton,
            ]}
            onPress={() => setActiveTab('saved')}
          >
            <Text
              style={[
                styles.tabButtonText,
                activeTab === 'saved' && styles.activeTabButtonText,
              ]}
            >
              {t('saved')}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 'stats' && styles.activeTabButton,
            ]}
            onPress={() => setActiveTab('stats')}
          >
            <Text
              style={[
                styles.tabButtonText,
                activeTab === 'stats' && styles.activeTabButtonText,
              ]}
            >
              {t('analytics')}
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Tab Content */}
        {activeTab === 'stories' && (
          <View style={styles.storiesContainer}>
            {mockStories.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>{t('noStories')}</Text>
                <TouchableOpacity style={styles.createButton}>
                  <Text style={styles.createButtonText}>{t('createStory')}</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <FlatList
                data={mockStories}
                renderItem={renderStoryItem}
                keyExtractor={(item) => item.id}
                numColumns={2}
                scrollEnabled={false}
                contentContainerStyle={styles.storiesGrid}
              />
            )}
          </View>
        )}
        
        {activeTab === 'saved' && (
          <View style={styles.savedContainer}>
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>{t('noSavedStories')}</Text>
              <TouchableOpacity style={styles.exploreButton}>
                <Text style={styles.exploreButtonText}>{t('exploreStories')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        
        {activeTab === 'stats' && (
          <View style={styles.statsTabContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statCardTitle}>{t('views')}</Text>
              <Text style={styles.statCardValue}>{userProfile.stats.views}</Text>
              <View style={styles.statTrend}>
                <Ionicons name="arrow-up" size={16} color="#4CAF50" />
                <Text style={styles.statTrendText}>12% {t('thisWeek')}</Text>
              </View>
            </View>
            
            <View style={styles.statCard}>
              <Text style={styles.statCardTitle}>{t('sales')}</Text>
              <Text style={styles.statCardValue}>{userProfile.stats.sales}</Text>
              <View style={styles.statTrend}>
                <Ionicons name="arrow-up" size={16} color="#4CAF50" />
                <Text style={styles.statTrendText}>8% {t('thisWeek')}</Text>
              </View>
            </View>
            
            <View style={styles.statCard}>
              <Text style={styles.statCardTitle}>{t('engagement')}</Text>
              <Text style={styles.statCardValue}>5.2%</Text>
              <View style={styles.statTrend}>
                <Ionicons name="arrow-down" size={16} color="#F44336" />
                <Text style={styles.statTrendText}>2% {t('thisWeek')}</Text>
              </View>
            </View>
            
            <View style={styles.statCard}>
              <Text style={styles.statCardTitle}>{t('rating')}</Text>
              <View style={styles.ratingContainer}>
                <Text style={styles.statCardValue}>{userProfile.stats.rating}</Text>
                <Ionicons name="star" size={20} color="#FFD700" />
              </View>
              <View style={styles.statTrend}>
                <Ionicons name="arrow-up" size={16} color="#4CAF50" />
                <Text style={styles.statTrendText}>0.2 {t('thisWeek')}</Text>
              </View>
            </View>
            
            <TouchableOpacity style={styles.viewMoreButton}>
              <Text style={styles.viewMoreText}>{t('viewDetailedAnalytics')}</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
      
      {renderEditProfileModal()}
      {renderSubscriptionModal()}
      {renderSettingsModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  coverContainer: {
    height: 200,
    position: 'relative',
  },
  coverPhoto: {
    width: '100%',
    height: '100%',
  },
  avatarContainer: {
    position: 'absolute',
    bottom: -50,
    left: 20,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: '#fff',
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#6200ee',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  profileInfo: {
    paddingTop: 60,
    paddingHorizontal: 20,
  },
  nameContainer: {
    marginBottom: 10,
  },
  fullName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
  },
  username: {
    fontSize: 16,
    color: '#666',
  },
  bioContainer: {
    marginBottom: 15,
  },
  bio: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  detailsContainer: {
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 10,
    padding: 15,
  },
  statColumn: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  actionsContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#6200ee',
    borderRadius: 5,
    paddingVertical: 10,
    alignItems: 'center',
    marginRight: 10,
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  secondaryButton: {
    flex: 0,
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 15,
    marginRight: 0,
  },
  subscriptionBanner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  subscriptionInfo: {
    flex: 1,
  },
  subscriptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  subscriptionDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  subscriptionAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subscriptionActionText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#6200ee',
    marginRight: 5,
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    marginBottom: 20,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#6200ee',
  },
  tabButtonText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabButtonText: {
    color: '#6200ee',
    fontWeight: 'bold',
  },
  storiesContainer: {
    paddingHorizontal: 10,
    marginBottom: 20,
  },
  storiesGrid: {
    paddingBottom: 20,
  },
  storyItem: {
    flex: 1,
    margin: 5,
    borderRadius: 10,
    overflow: 'hidden',
    backgroundColor: '#f9f9f9',
    borderWidth: 1,
    borderColor: '#eee',
  },
  storyImage: {
    width: '100%',
    height: 150,
  },
  storyInfo: {
    padding: 10,
  },
  storyTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  storyPrice: {
    fontSize: 14,
    color: '#6200ee',
    fontWeight: 'bold',
    marginBottom: 5,
  },
  storyStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 3,
  },
  savedContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 30,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  createButton: {
    backgroundColor: '#6200ee',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  createButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  exploreButton: {
    backgroundColor: '#6200ee',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  exploreButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  statsTabContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: '#f9f9f9',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#eee',
  },
  statCardTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  statCardValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  statTrend: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statTrendText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 5,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewMoreButton: {
    alignItems: 'center',
    marginTop: 10,
  },
  viewMoreText: {
    fontSize: 14,
    color: '#6200ee',
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    marginBottom: 15,
    color: '#333',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 5,
    marginHorizontal: 5,
  },
  primaryButton: {
    backgroundColor: '#6200ee',
  },
  modalButtonText: {
    color: '#666',
    fontWeight: 'bold',
  },
  primaryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  closeButton: {
    backgroundColor: '#f0f0f0',
    marginTop: 20,
  },
  closeButtonText: {
    color: '#666',
    fontWeight: 'bold',
  },
  planCard: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  activePlanCard: {
    borderColor: '#6200ee',
    backgroundColor: 'rgba(98, 0, 238, 0.05)',
  },
  planHeader: {
    marginBottom: 15,
  },
  planName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  planPrice: {
    fontSize: 16,
    color: '#666',
  },
  planFeatures: {
    marginBottom: 15,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  featureText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 10,
  },
  currentPlanButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 5,
    paddingVertical: 10,
    alignItems: 'center',
  },
  currentPlanText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  expiryText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginTop: 5,
  },
  subscribePlanButton: {
    backgroundColor: '#6200ee',
    borderRadius: 5,
    paddingVertical: 10,
    alignItems: 'center',
  },
  subscribePlanText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  settingSection: {
    marginBottom: 20,
  },
  settingSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  settingLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingLabel: {
    fontSize: 14,
    color: '#333',
    marginLeft: 10,
  },
  languageOptions: {
    flexDirection: 'row',
    marginTop: 10,
  },
  languageOption: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 10,
  },
  activeLanguageOption: {
    backgroundColor: '#6200ee',
  },
  languageText: {
    color: '#333',
  },
  activeLanguageText: {
    color: '#fff',
  },
  privacySelector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  privacyValue: {
    fontSize: 14,
    color: '#666',
    marginRight: 5,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    marginTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  logoutText: {
    fontSize: 16,
    color: '#F44336',
    fontWeight: 'bold',
    marginLeft: 10,
  },
});

export default ProfileScreen;