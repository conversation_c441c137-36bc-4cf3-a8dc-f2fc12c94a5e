import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ImageBackground,
  Dimensions,
  Platform,
} from 'react-native';
import { Button, Surface } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { useTranslation } from '../../i18n';

const { width, height } = Dimensions.get('window');

const WelcomeScreen = () => {
  const navigation = useNavigation();
  const theme = useSelector((state: RootState) => state.theme);
  const { t } = useTranslation();

  return (
    <ImageBackground
      source={{
        uri: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80'
      }}
      style={styles.background}
      blurRadius={2}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* Logo and Title */}
          <View style={styles.header}>
            <Text style={styles.logo}>🛍️</Text>
            <Text style={styles.title}>SaleStory</Text>
            <Text style={styles.subtitle}>{t('welcomeSubtitle')}</Text>
          </View>

          {/* Features */}
          <Surface style={[styles.featuresCard, { backgroundColor: theme.theme.colors.surface }]}>
            <View style={styles.feature}>
              <Text style={styles.featureIcon}>📱</Text>
              <Text style={[styles.featureText, { color: theme.theme.colors.onSurface }]}>
                {t('createStories')}
              </Text>
            </View>
            <View style={styles.feature}>
              <Text style={styles.featureIcon}>🛒</Text>
              <Text style={[styles.featureText, { color: theme.theme.colors.onSurface }]}>
                {t('sellProducts')}
              </Text>
            </View>
            <View style={styles.feature}>
              <Text style={styles.featureIcon}>💬</Text>
              <Text style={[styles.featureText, { color: theme.theme.colors.onSurface }]}>
                {t('connectBuyers')}
              </Text>
            </View>
          </Surface>

          {/* Action Buttons */}
          <View style={styles.actions}>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('Register' as never)}
              style={[styles.button, { backgroundColor: theme.theme.colors.primary }]}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}
            >
              {t('getStarted')}
            </Button>
            
            <Button
              mode="outlined"
              onPress={() => navigation.navigate('Login' as never)}
              style={[styles.button, { borderColor: theme.theme.colors.primary }]}
              contentStyle={styles.buttonContent}
              labelStyle={[styles.buttonLabel, { color: theme.theme.colors.primary }]}
            >
              {t('signIn')}
            </Button>
          </View>

          {/* Footer */}
          <Text style={styles.footer}>
            {t('welcomeFooter')}
          </Text>
        </View>
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    paddingTop: Platform.OS === 'web' ? 40 : 60,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    fontSize: 80,
    marginBottom: 10,
  },
  title: {
    fontSize: 42,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 10,
  },
  subtitle: {
    fontSize: 18,
    color: 'white',
    textAlign: 'center',
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 5,
  },
  featuresCard: {
    borderRadius: 20,
    padding: 30,
    marginBottom: 40,
    width: '100%',
    maxWidth: 350,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  featureIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  featureText: {
    fontSize: 16,
    flex: 1,
  },
  actions: {
    width: '100%',
    maxWidth: 350,
    marginBottom: 30,
  },
  button: {
    marginBottom: 15,
    borderRadius: 25,
  },
  buttonContent: {
    height: 50,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  footer: {
    fontSize: 14,
    color: 'white',
    textAlign: 'center',
    opacity: 0.8,
    marginTop: 20,
  },
});

export default WelcomeScreen;
