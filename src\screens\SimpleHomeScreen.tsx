import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';

// Mock data for stories
const mockStories = [
  {
    id: '1',
    username: 'fashionista',
    userAvatar: 'https://randomuser.me/api/portraits/women/43.jpg',
    image: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
    productName: 'Summer Dress Collection',
    price: '$59.99',
    likes: 1245,
  },
  {
    id: '2',
    username: 'techgadgets',
    userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    image: 'https://images.unsplash.com/photo-1546868871-7041f2a55e12?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
    productName: 'Smart Watch Pro',
    price: '$199.99',
    likes: 876,
  },
  {
    id: '3',
    username: 'homedesign',
    userAvatar: 'https://randomuser.me/api/portraits/women/65.jpg',
    image: 'https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
    productName: 'Modern Coffee Table',
    price: '$149.99',
    likes: 543,
  },
];

const SimpleHomeScreen = () => {
  const renderStoryCard = (story: any) => (
    <View key={story.id} style={styles.storyCard}>
      <View style={styles.userInfo}>
        <Image source={{ uri: story.userAvatar }} style={styles.userAvatar} />
        <Text style={styles.username}>{story.username}</Text>
      </View>
      
      <Image source={{ uri: story.image }} style={styles.storyImage} />
      
      <View style={styles.productInfo}>
        <Text style={styles.productName}>{story.productName}</Text>
        <Text style={styles.price}>{story.price}</Text>
        <View style={styles.actions}>
          <TouchableOpacity style={styles.likeButton}>
            <Text style={styles.likeText}>❤️ {story.likes}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buyButton}>
            <Text style={styles.buyButtonText}>Buy Now</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>🎉 SaleStory</Text>
        <Text style={styles.headerSubtitle}>قصص البيع</Text>
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {mockStories.map(renderStoryCard)}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#6200ee',
    padding: 20,
    paddingTop: Platform.OS === 'web' ? 20 : 50,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 10,
  },
  storyCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    overflow: 'hidden',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  storyImage: {
    width: '100%',
    height: 300,
    resizeMode: 'cover',
  },
  productInfo: {
    padding: 15,
  },
  productName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  price: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#6200ee',
    marginBottom: 15,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  likeButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  likeText: {
    fontSize: 16,
    color: '#666',
  },
  buyButton: {
    backgroundColor: '#6200ee',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
  },
  buyButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default SimpleHomeScreen;
