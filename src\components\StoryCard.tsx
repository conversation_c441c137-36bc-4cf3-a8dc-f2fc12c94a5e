import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  Animated,
  ImageBackground,
} from 'react-native';
import { Video } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from '../i18n';
import { Story } from '../store/slices/storySlice';
import { useDispatch } from 'react-redux';
import { likeStory, saveStory, incrementViews, shareStory } from '../store/slices/storySlice';

const { width, height } = Dimensions.get('window');

interface StoryCardProps {
  story: Story;
  onPress?: () => void;
  isActive: boolean;
  onComplete?: () => void;
  progressDuration?: number;
}

const StoryCard: React.FC<StoryCardProps> = ({
  story,
  onPress,
  isActive,
  onComplete,
  progressDuration = 5000, // Default duration is 5 seconds
}) => {
  const { t, isRTL } = useTranslation();
  const dispatch = useDispatch();
  const videoRef = useRef<Video>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentMediaIndex, setCurrentMediaIndex] = useState(0);
  const progressAnim = useRef(new Animated.Value(0)).current;
  const progressAnimation = useRef<Animated.CompositeAnimation | null>(null);

  // Handle video playback
  useEffect(() => {
    if (isActive) {
      if (story.media[currentMediaIndex].type === 'video' && videoRef.current) {
        videoRef.current.playAsync();
        setIsPlaying(true);
      }
      startProgressAnimation();
      // Increment view count when story becomes active
      dispatch(incrementViews(story.id));
    } else {
      if (story.media[currentMediaIndex].type === 'video' && videoRef.current) {
        videoRef.current.pauseAsync();
        setIsPlaying(false);
      }
      stopProgressAnimation();
    }

    return () => {
      stopProgressAnimation();
    };
  }, [isActive, currentMediaIndex]);

  const startProgressAnimation = () => {
    progressAnim.setValue(0);
    progressAnimation.current = Animated.timing(progressAnim, {
      toValue: 1,
      duration: progressDuration,
      useNativeDriver: false,
    });
    progressAnimation.current.start(({ finished }) => {
      if (finished) {
        handleProgressComplete();
      }
    });
  };

  const stopProgressAnimation = () => {
    if (progressAnimation.current) {
      progressAnimation.current.stop();
    }
  };

  const handleProgressComplete = () => {
    if (currentMediaIndex < story.media.length - 1) {
      // Move to next media in the story
      setCurrentMediaIndex(currentMediaIndex + 1);
    } else {
      // Story completed
      if (onComplete) {
        onComplete();
      }
    }
  };

  const handleLike = () => {
    dispatch(likeStory(story.id));
  };

  const handleSave = () => {
    dispatch(saveStory(story.id));
  };

  const handleShare = () => {
    dispatch(shareStory(story.id));
  };

  const renderMedia = () => {
    const media = story.media[currentMediaIndex];

    if (media.type === 'video') {
      return (
        <Video
          ref={videoRef}
          source={{ uri: media.url }}
          style={styles.media}
          resizeMode="cover"
          isLooping
          shouldPlay={isActive}
          onPlaybackStatusUpdate={(status) => {
            if (status.isLoaded) {
              setIsPlaying(status.isPlaying);
            }
          }}
        />
      );
    } else {
      return <Image source={{ uri: media.url }} style={styles.media} resizeMode="cover" />;
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={0.9}
      style={styles.container}
      onPress={onPress}
    >
      <View style={styles.storyContainer}>
        {/* Media Content */}
        {renderMedia()}

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          {story.media.map((_, index) => (
            <View key={index} style={styles.progressBarBackground}>
              <Animated.View
                style={[
                  styles.progressBar,
                  {
                    width: index === currentMediaIndex
                      ? progressAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: ['0%', '100%'],
                        })
                      : index < currentMediaIndex
                      ? '100%'
                      : '0%',
                  },
                ]}
              />
            </View>
          ))}
        </View>

        {/* User Info */}
        <View style={styles.userInfoContainer}>
          <Image source={{ uri: story.userAvatar }} style={styles.userAvatar} />
          <Text style={styles.username}>{story.username}</Text>
        </View>

        {/* Product Info */}
        <View style={styles.productInfoContainer}>
          <Text style={styles.productName}>{story.product.name}</Text>
          <Text style={styles.productPrice}>
            {story.product.price} {story.product.currency}
          </Text>
          <TouchableOpacity style={styles.buyButton}>
            <Text style={styles.buyButtonText}>{t('buyNow')}</Text>
          </TouchableOpacity>
        </View>

        {/* Action Buttons */}
        <View style={[styles.actionButtonsContainer, isRTL ? styles.actionButtonsRTL : {}]}>
          <TouchableOpacity style={styles.actionButton} onPress={handleLike}>
            <Ionicons
              name={story.liked ? 'heart' : 'heart-outline'}
              size={28}
              color={story.liked ? '#ff4081' : 'white'}
            />
            <Text style={styles.actionText}>{story.likes}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
            <Ionicons name="share-outline" size={28} color="white" />
            <Text style={styles.actionText}>{story.shares}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleSave}>
            <Ionicons
              name={story.saved ? 'bookmark' : 'bookmark-outline'}
              size={28}
              color={story.saved ? '#6200ee' : 'white'}
            />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width,
    height: height - 50, // Subtract tab bar height
  },
  storyContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  media: {
    ...StyleSheet.absoluteFillObject,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    paddingTop: 10,
    position: 'absolute',
    top: 40, // Below status bar
    width: '100%',
    zIndex: 10,
  },
  progressBarBackground: {
    flex: 1,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 2,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 3,
  },
  userInfoContainer: {
    position: 'absolute',
    top: 60,
    left: 10,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    zIndex: 10,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'white',
  },
  username: {
    color: 'white',
    marginLeft: 10,
    fontWeight: 'bold',
    fontSize: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  productInfoContainer: {
    position: 'absolute',
    bottom: 100,
    left: 10,
    padding: 15,
    borderRadius: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    width: '70%',
    zIndex: 10,
  },
  productName: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  productPrice: {
    color: 'white',
    fontSize: 16,
    marginBottom: 10,
  },
  buyButton: {
    backgroundColor: '#6200ee',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  buyButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  actionButtonsContainer: {
    position: 'absolute',
    right: 10,
    bottom: 100,
    alignItems: 'center',
    zIndex: 10,
  },
  actionButtonsRTL: {
    right: undefined,
    left: 10,
  },
  actionButton: {
    alignItems: 'center',
    marginBottom: 15,
  },
  actionText: {
    color: 'white',
    marginTop: 5,
    fontSize: 12,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
});

export default StoryCard;