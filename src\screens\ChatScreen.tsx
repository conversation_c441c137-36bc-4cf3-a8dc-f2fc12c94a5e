import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Modal,
  Alert,
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from '../i18n';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { useNavigation } from '@react-navigation/native';

// Define message types
type MessageType = 'text' | 'image' | 'product';

// Define message interface
interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  type: MessageType;
  timestamp: string;
  read: boolean;
  translated?: string;
}

// Define conversation interface
interface Conversation {
  id: string;
  userId: string;
  username: string;
  userAvatar: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  isOnline: boolean;
  rating?: number;
  isSeller: boolean;
}

// Mock conversations data
const mockConversations: Conversation[] = [
  {
    id: 'conv1',
    userId: 'user1',
    username: '<PERSON>',
    userAvatar: 'https://randomuser.me/api/portraits/women/43.jpg',
    lastMessage: 'Is this product still available?',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
    unreadCount: 2,
    isOnline: true,
    rating: 4.8,
    isSeller: false,
  },
  {
    id: 'conv2',
    userId: 'user2',
    username: 'Mohammed Ali',
    userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    lastMessage: 'Thank you for your purchase!',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    unreadCount: 0,
    isOnline: false,
    rating: 4.5,
    isSeller: true,
  },
  {
    id: 'conv3',
    userId: 'user3',
    username: 'Fatima Hassan',
    userAvatar: 'https://randomuser.me/api/portraits/women/65.jpg',
    lastMessage: 'Can you ship to Dubai?',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    unreadCount: 0,
    isOnline: true,
    rating: 4.9,
    isSeller: false,
  },
  {
    id: 'conv4',
    userId: 'user4',
    username: 'Khalid Omar',
    userAvatar: 'https://randomuser.me/api/portraits/men/22.jpg',
    lastMessage: 'I\'ll send you more photos',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
    unreadCount: 0,
    isOnline: false,
    rating: 4.2,
    isSeller: true,
  },
  {
    id: 'conv5',
    userId: 'user5',
    username: 'Layla Mahmoud',
    userAvatar: 'https://randomuser.me/api/portraits/women/22.jpg',
    lastMessage: 'Do you have this in blue?',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(), // 2 days ago
    unreadCount: 0,
    isOnline: false,
    rating: 4.7,
    isSeller: false,
  },
];

// Mock messages for a conversation
const generateMockMessages = (conversationId: string): Message[] => {
  const messages: Message[] = [];
  const currentUserId = 'currentUser'; // Assuming this is the current user's ID
  const otherUserId = conversationId === 'conv1' ? 'user1' : 'user2';
  
  // Generate different message patterns based on conversation ID
  if (conversationId === 'conv1') {
    messages.push(
      {
        id: '1',
        senderId: otherUserId,
        receiverId: currentUserId,
        content: 'Hello! I saw your Summer Dress Collection and I love it!',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
        read: true,
      },
      {
        id: '2',
        senderId: currentUserId,
        receiverId: otherUserId,
        content: 'Thank you! I\'m glad you like it.',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 55).toISOString(),
        read: true,
      },
      {
        id: '3',
        senderId: otherUserId,
        receiverId: currentUserId,
        content: 'Is this product still available?',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 50).toISOString(),
        read: true,
      },
      {
        id: '4',
        senderId: otherUserId,
        receiverId: currentUserId,
        content: 'And do you have it in size medium?',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 49).toISOString(),
        read: false,
      },
      {
        id: '5',
        senderId: otherUserId,
        receiverId: currentUserId,
        content: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
        type: 'image',
        timestamp: new Date(Date.now() - 1000 * 60 * 48).toISOString(),
        read: false,
      }
    );
  } else if (conversationId === 'conv2') {
    messages.push(
      {
        id: '1',
        senderId: currentUserId,
        receiverId: otherUserId,
        content: 'Hi, I\'m interested in your Smart Watch Pro.',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
        read: true,
      },
      {
        id: '2',
        senderId: otherUserId,
        receiverId: currentUserId,
        content: "Hello! Yes, it's still available. It comes with a 1-year warranty.",
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 115).toISOString(),
        read: true,
      },
      {
        id: '3',
        senderId: currentUserId,
        receiverId: otherUserId,
        content: "Great! I'd like to purchase it. Can you ship to Riyadh?",
        type: 'text',

        timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
        read: true,
      },
      {
        id: '4',
        senderId: otherUserId,
        receiverId: currentUserId,
        content: 'Yes, we offer free shipping to Riyadh. It should arrive within 2-3 business days.',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 55).toISOString(),
        read: true,
      },
      {
        id: '5',
        senderId: otherUserId,
        receiverId: currentUserId,
        content: 'Thank you for your purchase!',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        read: true,
      },
    );
  } else {
    // Default conversation
    messages.push(
      {
        id: '1',
        senderId: otherUserId,
        receiverId: currentUserId,
        content: 'Hello there!',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        read: true,
      },
      {
        id: '2',
        senderId: currentUserId,
        receiverId: otherUserId,
        content: 'Hi! How can I help you?',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 1.5).toISOString(),
        read: true,
      },
    );
  }
  
  return messages;
};

const ChatScreen = () => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const theme = useSelector((state: RootState) => state.theme);
  
  // State for conversations list and active conversation
  const [conversations, setConversations] = useState<Conversation[]>(mockConversations);
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [messageText, setMessageText] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showTranslation, setShowTranslation] = useState(false);
  const [isReportModalVisible, setIsReportModalVisible] = useState(false);
  const [reportReason, setReportReason] = useState('');
  
  const flatListRef = useRef<FlatList>(null);
  
  // Filter conversations based on search query
  const filteredConversations = conversations.filter((conv) =>
    conv.username.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  // Load messages when active conversation changes
  useEffect(() => {
    if (activeConversation) {
      setLoading(true);
      // Simulate API call with timeout
      setTimeout(() => {
        const mockMessages = generateMockMessages(activeConversation.id);
        setMessages(mockMessages);
        setLoading(false);
        
        // Mark messages as read
        if (activeConversation.unreadCount > 0) {
          const updatedConversations = conversations.map((conv) =>
            conv.id === activeConversation.id
              ? { ...conv, unreadCount: 0 }
              : conv
          );
          setConversations(updatedConversations);
        }
      }, 1000);
    }
  }, [activeConversation]);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    if (messages.length > 0 && flatListRef.current) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 200);
    }
  }, [messages]);
  
  const handleSendMessage = () => {
    if (!messageText.trim() || !activeConversation) return;
    
    const newMessage: Message = {
      id: Date.now().toString(),
      senderId: 'currentUser',
      receiverId: activeConversation.userId,
      content: messageText.trim(),
      type: 'text',
      timestamp: new Date().toISOString(),
      read: false,
    };
    
    setMessages([...messages, newMessage]);
    setMessageText('');
    
    // Update last message in conversation list
    const updatedConversations = conversations.map((conv) =>
      conv.id === activeConversation.id
        ? {
            ...conv,
            lastMessage: messageText.trim(),
            lastMessageTime: new Date().toISOString(),
          }
        : conv
    );
    setConversations(updatedConversations);
  };
  
  const handleImageSend = () => {
    // In a real app, this would open the image picker
    Alert.alert(t('featureNotAvailable'), t('comingSoon'));
  };
  
  const toggleTranslation = (message: Message) => {
    // In a real app, this would call a translation API
    if (!message.translated) {
      const updatedMessages = messages.map((msg) =>
        msg.id === message.id
          ? { ...msg, translated: `[${t('translated')}] ${msg.content}` }
          : msg
      );
      setMessages(updatedMessages);
    }
    setShowTranslation(!showTranslation);
  };
  
  const handleReportUser = () => {
    if (!reportReason.trim() || !activeConversation) {
      Alert.alert(t('error'), t('pleaseProvideReason'));
      return;
    }
    
    // In a real app, this would send the report to the server
    Alert.alert(
      t('reportSubmitted'),
      t('reportThankYou'),
      [{ text: t('ok'), onPress: () => setIsReportModalVisible(false) }]
    );
    setReportReason('');
  };
  
  const handleBlockUser = () => {
    if (!activeConversation) return;
    
    Alert.alert(
      t('blockUser'),
      t('blockUserConfirmation', { username: activeConversation.username }),
      [
        { text: t('cancel'), style: 'cancel' },
        {
          text: t('block'),
          style: 'destructive',
          onPress: () => {
            // In a real app, this would call an API to block the user
            const updatedConversations = conversations.filter(
              (conv) => conv.id !== activeConversation.id
            );
            setConversations(updatedConversations);
            setActiveConversation(null);
            Alert.alert(t('userBlocked'), t('userBlockedSuccess'));
          },
        },
      ]
    );
  };
  
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInDays = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (diffInDays === 0) {
      // Today: show time
      return date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });
    } else if (diffInDays === 1) {
      // Yesterday
      return t('yesterday');
    } else if (diffInDays < 7) {
      // Within a week: show day name
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      // Older: show date
      return date.toLocaleDateString([], {
        month: 'short',
        day: 'numeric',
      });
    }
  };
  
  const renderConversationItem = ({ item }: { item: Conversation }) => (
    <TouchableOpacity
      style={[
        styles.conversationItem,
        activeConversation?.id === item.id && styles.activeConversation,
      ]}
      onPress={() => setActiveConversation(item)}
    >
      <View style={styles.avatarContainer}>
        <Image source={{ uri: item.userAvatar }} style={styles.avatar} />
        {item.isOnline && <View style={styles.onlineIndicator} />}
      </View>
      
      <View style={styles.conversationInfo}>
        <View style={styles.conversationHeader}>
          <Text style={styles.username} numberOfLines={1}>
            {item.username}
          </Text>
          <Text style={styles.timeText}>{formatTime(item.lastMessageTime)}</Text>
        </View>
        
        <View style={styles.conversationFooter}>
          <Text style={styles.lastMessage} numberOfLines={1}>
            {item.lastMessage}
          </Text>
          
          {item.unreadCount > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadText}>{item.unreadCount}</Text>
            </View>
          )}
        </View>
        
        {item.rating && (
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color="#FFD700" />
            <Text style={styles.ratingText}>{item.rating.toFixed(1)}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
  
  const renderMessageItem = ({ item }: { item: Message }) => {
    const isCurrentUser = item.senderId === 'currentUser';
    
    return (
      <View
        style={[
          styles.messageContainer,
          isCurrentUser ? styles.sentMessage : styles.receivedMessage,
        ]}
      >
        {item.type === 'text' ? (
          <View
            style={[
              styles.messageBubble,
              isCurrentUser ? styles.sentBubble : styles.receivedBubble,
            ]}
          >
            <Text style={styles.messageText}>
              {showTranslation && item.translated ? item.translated : item.content}
            </Text>
            <Text style={styles.messageTime}>{formatTime(item.timestamp)}</Text>
            
            {!isCurrentUser && (
              <TouchableOpacity
                style={styles.translateButton}
                onPress={() => toggleTranslation(item)}
              >
                <MaterialIcons name="translate" size={16} color="#6200ee" />
              </TouchableOpacity>
            )}
          </View>
        ) : item.type === 'image' ? (
          <View
            style={[
              styles.imageBubble,
              isCurrentUser ? styles.sentBubble : styles.receivedBubble,
            ]}
          >
            <Image source={{ uri: item.content }} style={styles.messageImage} />
            <Text style={styles.messageTime}>{formatTime(item.timestamp)}</Text>
          </View>
        ) : null}
        
        {isCurrentUser && (
          <View style={styles.readStatus}>
            <Ionicons
              name={item.read ? "checkmark-done" : "checkmark"}
              size={16}
              color={item.read ? "#6200ee" : "#999"}
            />
          </View>
        )}
      </View>
    );
  };
  
  const renderConversationsList = () => (
    <View style={styles.conversationsContainer}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder={t('searchConversations')}
          placeholderTextColor="#999"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
      
      {filteredConversations.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>{t('noConversations')}</Text>
        </View>
      ) : (
        <FlatList
          data={filteredConversations}
          renderItem={renderConversationItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
  
  const renderChatView = () => (
    <KeyboardAvoidingView
      style={styles.chatContainer}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <View style={styles.chatHeader}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => setActiveConversation(null)}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        
        <Image
          source={{ uri: activeConversation?.userAvatar }}
          style={styles.chatAvatar}
        />
        
        <View style={styles.chatHeaderInfo}>
          <Text style={styles.chatUsername}>{activeConversation?.username}</Text>
          <Text style={styles.chatStatus}>
            {activeConversation?.isOnline ? t('online') : t('offline')}
          </Text>
        </View>
        
        <TouchableOpacity
          style={styles.moreButton}
          onPress={() => setIsReportModalVisible(true)}
        >
          <Ionicons name="ellipsis-vertical" size={24} color="#333" />
        </TouchableOpacity>
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6200ee" />
        </View>
      ) : (
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessageItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
        />
      )}
      
      <View style={styles.inputContainer}>
        <TouchableOpacity style={styles.attachButton} onPress={handleImageSend}>
          <Ionicons name="image" size={24} color="#6200ee" />
        </TouchableOpacity>
        
        <TextInput
          style={styles.input}
          placeholder={t('typeMessage')}
          placeholderTextColor="#999"
          value={messageText}
          onChangeText={setMessageText}
          multiline
        />
        
        <TouchableOpacity
          style={[styles.sendButton, !messageText.trim() && styles.disabledButton]}
          onPress={handleSendMessage}
          disabled={!messageText.trim()}
        >
          <Ionicons name="send" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
  
  const renderReportModal = () => (
    <Modal
      visible={isReportModalVisible}
      transparent
      animationType="slide"
      onRequestClose={() => setIsReportModalVisible(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>{t('reportOrBlock')}</Text>
          
          <Text style={styles.modalLabel}>{t('reportReason')}:</Text>
          <TextInput
            style={styles.reportInput}
            placeholder={t('enterReportReason')}
            placeholderTextColor="#999"
            value={reportReason}
            onChangeText={setReportReason}
            multiline
          />
          
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => setIsReportModalVisible(false)}
            >
              <Text style={styles.modalButtonText}>{t('cancel')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.modalButton, styles.reportButton]}
              onPress={handleReportUser}
            >
              <Text style={styles.reportButtonText}>{t('report')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.modalButton, styles.blockButton]}
              onPress={handleBlockUser}
            >
              <Text style={styles.blockButtonText}>{t('block')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
  
  return (
    <View style={styles.container}>
      {activeConversation ? renderChatView() : renderConversationsList()}
      {renderReportModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  conversationsContainer: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    margin: 15,
    paddingHorizontal: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    color: '#333',
  },
  conversationItem: {
    flexDirection: 'row',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  activeConversation: {
    backgroundColor: '#f0f0f0',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 15,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#fff',
  },
  conversationInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  timeText: {
    fontSize: 12,
    color: '#999',
  },
  conversationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  unreadBadge: {
    backgroundColor: '#6200ee',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unreadText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  ratingText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 3,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
  chatContainer: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    marginRight: 15,
  },
  chatAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  chatHeaderInfo: {
    flex: 1,
  },
  chatUsername: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  chatStatus: {
    fontSize: 12,
    color: '#999',
  },
  moreButton: {
    padding: 5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messagesContainer: {
    padding: 15,
    paddingBottom: 20,
  },
  messageContainer: {
    marginBottom: 15,
    maxWidth: '80%',
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  sentMessage: {
    alignSelf: 'flex-end',
  },
  receivedMessage: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    borderRadius: 20,
    padding: 12,
    paddingBottom: 25,
  },
  sentBubble: {
    backgroundColor: '#6200ee',
  },
  receivedBubble: {
    backgroundColor: '#f0f0f0',
  },
  messageText: {
    fontSize: 16,
    color: '#fff',
  },
  receivedMessageContainer: {
    alignSelf: 'flex-start',
  },
  incomingMessageBubble: {
    backgroundColor: '#f0f0f0',
  },
  receivedBubbleStyle: {
    backgroundColor: '#f0f0f0',
  },
  message: {
    fontSize: 16,
  },
  messageStyle: {
    fontSize: 16,
    color: '#fff',
  },
  receivedMessageBubble: {
    backgroundColor: '#f0f0f0',
  },
  messageContainer: {
    fontSize: 16,
    color: '#333',
  },
  // Removed duplicate receivedMessageBubble style since receivedBubble already exists
    backgroundColor: '#f0f0f0',
  },
  messageTextStyle: {
    fontSize: 16,
    color: '#333',
  },
  messageTime: {
    fontSize: 10,
    color: 'rgba(255, 255, 255, 0.7)',
    position: 'absolute',
    right: 12,
    bottom: 6,
  },
  receivedBubble: {
    backgroundColor: '#f0f0f0',
  },
  messageText: {
    fontSize: 16,
    color: '#333',
  },
  messageTime: {
    fontSize: 10,
    color: 'rgba(0, 0, 0, 0.5)',
    position: 'absolute',
    right: 12,
    bottom: 6,
  },
  translateButton: {
    position: 'absolute',
    right: 30,
    bottom: 5,
  },
  imageBubble: {
    borderRadius: 15,
    overflow: 'hidden',
    padding: 0,
    paddingBottom: 15,
  },
  messageImage: {
    width: 200,
    height: 200,
    borderRadius: 15,
  },
  readStatus: {
    marginLeft: 5,
    marginBottom: 5,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  attachButton: {
    padding: 10,
  },
  input: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginHorizontal: 10,
    maxHeight: 100,
    color: '#333',
  },
  sendButton: {
    backgroundColor: '#6200ee',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    width: '80%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  modalLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 10,
  },
  reportInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 10,
    height: 100,
    textAlignVertical: 'top',
    marginBottom: 20,
    color: '#333',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  reportButton: {
    backgroundColor: '#FF9800',
  },
  blockButton: {
    backgroundColor: '#F44336',
  },
  modalButtonText: {
    color: '#666',
    fontWeight: 'bold',
  },
  reportButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  blockButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default ChatScreen;