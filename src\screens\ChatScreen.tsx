import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Modal,
  Alert,
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from '../i18n';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { useNavigation } from '@react-navigation/native';

// Define message types
type MessageType = 'text' | 'image' | 'product';

// Define message interface
interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  type: MessageType;
  timestamp: string;
  read: boolean;
  translated?: string;
}

// Define conversation interface
interface Conversation {
  id: string;
  userId: string;
  username: string;
  userAvatar: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  isOnline: boolean;
  rating?: number;
  isSeller: boolean;
}

// Mock conversations data
const mockConversations: Conversation[] = [
  {
    id: 'conv1',
    userId: 'user1',
    username: '<PERSON>',
    userAvatar: 'https://randomuser.me/api/portraits/women/43.jpg',
    lastMessage: 'Is this product still available?',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
    unreadCount: 2,
    isOnline: true,
    rating: 4.8,
    isSeller: false,
  },
  {
    id: 'conv2',
    userId: 'user2',
    username: 'Mohammed Ali',
    userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    lastMessage: 'Thank you for your purchase!',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
    unreadCount: 0,
    isOnline: false,
    rating: 4.5,
    isSeller: true,
  },
  {
    id: 'conv3',
    userId: 'user3',
    username: 'Fatima Hassan',
    userAvatar: 'https://randomuser.me/api/portraits/women/65.jpg',
    lastMessage: 'Can you ship to Dubai?',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
    unreadCount: 0,
    isOnline: true,
    rating: 4.9,
    isSeller: false,
  },
];

// Mock messages for a conversation
const generateMockMessages = (conversationId: string): Message[] => {
  const messages: Message[] = [];
  const currentUserId = 'currentUser';
  const otherUserId = conversationId === 'conv1' ? 'user1' : 'user2';
  
  if (conversationId === 'conv1') {
    messages.push(
      {
        id: '1',
        senderId: otherUserId,
        receiverId: currentUserId,
        content: 'Hello! I saw your Summer Dress Collection and I love it!',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
        read: true,
      },
      {
        id: '2',
        senderId: currentUserId,
        receiverId: otherUserId,
        content: 'Thank you! I\'m glad you like it.',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 55).toISOString(),
        read: true,
      },
      {
        id: '3',
        senderId: otherUserId,
        receiverId: currentUserId,
        content: 'Is this product still available?',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 50).toISOString(),
        read: true,
      }
    );
  } else {
    messages.push(
      {
        id: '1',
        senderId: otherUserId,
        receiverId: currentUserId,
        content: 'Hello there!',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        read: true,
      },
      {
        id: '2',
        senderId: currentUserId,
        receiverId: otherUserId,
        content: 'Hi! How can I help you?',
        type: 'text',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 1.5).toISOString(),
        read: true,
      }
    );
  }
  
  return messages;
};

const ChatScreen = () => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const theme = useSelector((state: RootState) => state.theme);
  
  const [conversations, setConversations] = useState<Conversation[]>(mockConversations);
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [messageText, setMessageText] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showTranslation, setShowTranslation] = useState(false);
  const [isReportModalVisible, setIsReportModalVisible] = useState(false);
  const [reportReason, setReportReason] = useState('');
  
  const flatListRef = useRef<FlatList>(null);
  
  const filteredConversations = conversations.filter((conv) =>
    conv.username.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  useEffect(() => {
    if (activeConversation) {
      setLoading(true);
      setTimeout(() => {
        const mockMessages = generateMockMessages(activeConversation.id);
        setMessages(mockMessages);
        setLoading(false);
        
        if (activeConversation.unreadCount > 0) {
          const updatedConversations = conversations.map((conv) =>
            conv.id === activeConversation.id
              ? { ...conv, unreadCount: 0 }
              : conv
          );
          setConversations(updatedConversations);
        }
      }, 1000);
    }
  }, [activeConversation]);
  
  useEffect(() => {
    if (messages.length > 0 && flatListRef.current) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 200);
    }
  }, [messages]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Chat Screen</Text>
      <Text style={styles.subtitle}>Coming Soon!</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
});

export default ChatScreen;
