import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';

// Import reducers
import userReducer from '@/store/slices/userSlice';
import storyReducer from '@/store/slices/storySlice';
import themeReducer from '@/store/slices/themeSlice';
import languageReducer from '@/store/slices/languageSlice';

// Configure the store
export const store = configureStore({
  reducer: {
    user: userReducer,
    stories: storyReducer,
    theme: themeReducer,
    language: languageReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

// Enable listener behavior for the store
setupListeners(store.dispatch);

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;