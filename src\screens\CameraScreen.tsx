import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Modal,
  TextInput,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Camera } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import { Ionicons, MaterialIcons, FontAwesome5 } from '@expo/vector-icons';
import { useTranslation } from '../i18n';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { useNavigation } from '@react-navigation/native';

// Define filter types
type Filter = {
  id: string;
  name: string;
  icon: string;
  category: 'beauty' | 'food' | 'fashion' | 'tech' | 'general';
};

// Define product information type
type ProductInfo = {
  name: string;
  price: string;
  description: string;
  category: string;
  tags: string[];
};

const CameraScreen = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const theme = useSelector((state: RootState) => state.theme);
  
  // Camera and permissions state
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [cameraType, setCameraType] = useState(Camera.Constants.Type.back);
  const [flashMode, setFlashMode] = useState(Camera.Constants.FlashMode.off);
  const [isRecording, setIsRecording] = useState(false);
  const cameraRef = useRef<Camera>(null);
  
  // Media state
  const [capturedMedia, setCapturedMedia] = useState<string[]>([]);
  const [currentMediaIndex, setCurrentMediaIndex] = useState(0);
  const [isMediaPreview, setIsMediaPreview] = useState(false);
  
  // Editing state
  const [selectedFilter, setSelectedFilter] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editText, setEditText] = useState('');
  const [textPosition, setTextPosition] = useState({ x: 150, y: 150 });
  const [textStyle, setTextStyle] = useState({ fontSize: 20, color: '#FFFFFF' });
  
  // Product information state
  const [isProductInfoModalVisible, setIsProductInfoModalVisible] = useState(false);
  const [productInfo, setProductInfo] = useState<ProductInfo>({
    name: '',
    price: '',
    description: '',
    category: 'fashion',
    tags: [],
  });
  
  // Publishing state
  const [isPublishing, setIsPublishing] = useState(false);
  
  // Sample filters
  const filters: Filter[] = [
    { id: 'f1', name: t('noFilter'), icon: 'image-outline', category: 'general' },
    { id: 'f2', name: t('beauty'), icon: 'face-woman', category: 'beauty' },
    { id: 'f3', name: t('food'), icon: 'food', category: 'food' },
    { id: 'f4', name: t('fashion'), icon: 'tshirt-crew', category: 'fashion' },
    { id: 'f5', name: t('tech'), icon: 'laptop', category: 'tech' },
    { id: 'f6', name: t('vintage'), icon: 'camera-vintage', category: 'general' },
    { id: 'f7', name: t('blackAndWhite'), icon: 'contrast-box', category: 'general' },
    { id: 'f8', name: t('warm'), icon: 'white-balance-sunny', category: 'general' },
    { id: 'f9', name: t('cool'), icon: 'snowflake', category: 'general' },
  ];
  
  // Request camera and media library permissions
  useEffect(() => {
    (async () => {
      const { status: cameraStatus } = await Camera.requestCameraPermissionsAsync();
      const { status: mediaStatus } = await MediaLibrary.requestPermissionsAsync();
      setHasPermission(
        cameraStatus === 'granted' && mediaStatus === 'granted'
      );
    })();
  }, []);
  
  // Handle permission denied
  if (hasPermission === null) {
    return (
      <View style={styles.permissionContainer}>
        <ActivityIndicator size="large" color="#6200ee" />
      </View>
    );
  }
  
  if (hasPermission === false) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>{t('cameraPermissionDenied')}</Text>
        <TouchableOpacity
          style={styles.permissionButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.permissionButtonText}>{t('goBack')}</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  // Take a photo
  const takePicture = async () => {
    if (!cameraRef.current) return;
    
    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
      });
      
      setCapturedMedia([...capturedMedia, photo.uri]);
      setCurrentMediaIndex(capturedMedia.length);
      setIsMediaPreview(true);
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert(t('error'), t('failedToTakePicture'));
    }
  };
  
  // Start/stop video recording
  const toggleRecording = async () => {
    if (!cameraRef.current) return;
    
    if (isRecording) {
      cameraRef.current.stopRecording();
      setIsRecording(false);
    } else {
      setIsRecording(true);
      
      try {
        const videoRecordPromise = cameraRef.current.recordAsync({
          maxDuration: 30, // 30 seconds max
          quality: '720p',
          mute: false,
        });
        
        const video = await videoRecordPromise;
        setCapturedMedia([...capturedMedia, video.uri]);
        setCurrentMediaIndex(capturedMedia.length);
        setIsMediaPreview(true);
      } catch (error) {
        console.error('Error recording video:', error);
        setIsRecording(false);
        Alert.alert(t('error'), t('failedToRecordVideo'));
      }
    }
  };
  
  // Pick image from gallery
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: false,
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        setCapturedMedia([...capturedMedia, result.assets[0].uri]);
        setCurrentMediaIndex(capturedMedia.length);
        setIsMediaPreview(true);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(t('error'), t('failedToPickImage'));
    }
  };
  
  // Toggle camera type (front/back)
  const toggleCameraType = () => {
    setCameraType(
      cameraType === 'back'
        ? 'front'
        : 'back'
    );
  };
  
  // Toggle flash mode
  const toggleFlashMode = () => {
    setFlashMode(
      flashMode === 'off'
        ? 'on'
        : 'off'
    );
  };
  
  // Apply filter to image
  const applyFilter = (filterId: string) => {
    setSelectedFilter(filterId);
    // In a real app, this would apply actual image filters
  };
  
  // Add text to image
  const addText = () => {
    setIsEditMode(true);
  };
  
  // Update product information
  const updateProductInfo = (key: keyof ProductInfo, value: string | string[]) => {
    setProductInfo({ ...productInfo, [key]: value });
  };
  
  // Add tag to product
  const addTag = (tag: string) => {
    if (tag.trim() && !productInfo.tags.includes(tag.trim())) {
      updateProductInfo('tags', [...productInfo.tags, tag.trim()]);
    }
  };
  
  // Remove tag from product
  const removeTag = (index: number) => {
    const newTags = [...productInfo.tags];
    newTags.splice(index, 1);
    updateProductInfo('tags', newTags);
  };
  
  // Publish story
  const publishStory = async () => {
    if (capturedMedia.length === 0) {
      Alert.alert(t('error'), t('noMediaCaptured'));
      return;
    }
    
    if (!productInfo.name || !productInfo.price) {
      Alert.alert(t('error'), t('productInfoRequired'));
      return;
    }
    
    setIsPublishing(true);
    
    // Simulate API call with timeout
    setTimeout(() => {
      setIsPublishing(false);
      Alert.alert(
        t('success'),
        t('storyPublished'),
        [
          {
            text: t('ok'),
            onPress: () => {
              // Reset state and navigate to home
              setCapturedMedia([]);
              setIsMediaPreview(false);
              setSelectedFilter(null);
              setEditText('');
              setProductInfo({
                name: '',
                price: '',
                description: '',
                category: 'fashion',
                tags: [],
              });
              navigation.navigate('Home' as never);
            },
          },
        ]
      );
    }, 2000);
  };
  
  // Render camera view
  const renderCameraView = () => (
    <View style={styles.cameraContainer}>
      <Camera as any
        ref={cameraRef}
        style={styles.camera}
        type={cameraType}
        flashMode={flashMode}
        ratio="16:9"
      >
        <View style={styles.cameraControls}>
          <View style={styles.cameraTopControls}>
            <TouchableOpacity
              style={styles.cameraButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="close" size={28} color="#FFFFFF" />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.cameraButton}
              onPress={toggleFlashMode}
            >
              <Ionicons
                name={flashMode === 'on' ? "flash" : "flash-off"}
                size={28}
                color="#FFFFFF"
              />
            </TouchableOpacity>
          </View>
          
          <View style={styles.cameraBottomControls}>
            <TouchableOpacity style={styles.galleryButton} onPress={pickImage}>
              <Ionicons name="images" size={28} color="#FFFFFF" />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.captureButton, isRecording && styles.recordingButton]}
              onPress={isRecording ? toggleRecording : takePicture}
              onLongPress={toggleRecording}
            >
              {isRecording ? (
                <View style={styles.recordingIndicator} />
              ) : null}
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.flipButton}
              onPress={toggleCameraType}
            >
              <Ionicons name="camera-reverse" size={28} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>
      </Camera>
    </View>
  );
  
  // Render media preview
  const renderMediaPreview = () => {
    const currentMedia = capturedMedia[currentMediaIndex];
    const isVideo = currentMedia?.endsWith('.mp4');
    
    return (
      <View style={styles.previewContainer}>
        <Image source={{ uri: currentMedia }} style={styles.previewImage} />
        
        {/* Text overlay if in edit mode */}
        {isEditMode && editText ? (
          <View
            style={[
              styles.textOverlay,
              { top: textPosition.y, left: textPosition.x },
            ]}
          >
            <Text style={[styles.overlayText, textStyle]}>{editText}</Text>
          </View>
        ) : null}
        
        <View style={styles.previewControls}>
          <TouchableOpacity
            style={styles.previewButton}
            onPress={() => {
              setIsMediaPreview(false);
              setIsEditMode(false);
              setEditText('');
            }}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.previewButton}
            onPress={() => setIsProductInfoModalVisible(true)}
          >
            <Ionicons name="pricetag" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
        
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.filtersContainer}
        >
          {filters.map((filter) => (
            <TouchableOpacity
              key={filter.id}
              style={[
                styles.filterItem,
                selectedFilter === filter.id && styles.selectedFilter,
              ]}
              onPress={() => applyFilter(filter.id)}
            >
              <MaterialIcons name={filter.icon as any} size={24} color="#FFFFFF" />
              <Text style={styles.filterName}>{filter.name}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        <View style={styles.editToolsContainer}>
          <TouchableOpacity style={styles.editTool} onPress={addText}>
            <MaterialIcons name="text-fields" size={24} color="#FFFFFF" />
            <Text style={styles.editToolText}>{t('text')}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.editTool}>
            <MaterialIcons name="emoji-emotions" size={24} color="#FFFFFF" />
            <Text style={styles.editToolText}>{t('stickers')}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.editTool}>
            <MaterialIcons name="music-note" size={24} color="#FFFFFF" />
            <Text style={styles.editToolText}>{t('music')}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.editTool}>
            <MaterialIcons name="crop" size={24} color="#FFFFFF" />
            <Text style={styles.editToolText}>{t('crop')}</Text>
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity
          style={styles.publishButton}
          onPress={() => setIsProductInfoModalVisible(true)}
        >
          <Text style={styles.publishButtonText}>{t('next')}</Text>
        </TouchableOpacity>
      </View>
    );
  };
  
  // Render text input modal
  const renderTextInputModal = () => (
    <Modal
      visible={isEditMode}
      transparent
      animationType="slide"
      onRequestClose={() => setIsEditMode(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.textInputContainer}>
          <TextInput
            style={styles.textInput}
            placeholder={t('enterText')}
            placeholderTextColor="#999"
            value={editText}
            onChangeText={setEditText}
            multiline
            autoFocus
          />
          
          <View style={styles.textStyleControls}>
            <TouchableOpacity
              style={styles.textStyleButton}
              onPress={() =>
                setTextStyle({
                  ...textStyle,
                  fontSize: textStyle.fontSize + 2,
                })
              }
            >
              <Ionicons name="add" size={24} color="#333" />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.textStyleButton}
              onPress={() =>
                setTextStyle({
                  ...textStyle,
                  fontSize: Math.max(10, textStyle.fontSize - 2),
                })
              }
            >
              <Ionicons name="remove" size={24} color="#333" />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.textStyleButton, { backgroundColor: '#FF5252' }]}
              onPress={() => setTextStyle({ ...textStyle, color: '#FF5252' })}
            />
            
            <TouchableOpacity
              style={[styles.textStyleButton, { backgroundColor: '#FFFFFF' }]}
              onPress={() => setTextStyle({ ...textStyle, color: '#FFFFFF' })}
            />
            
            <TouchableOpacity
              style={[styles.textStyleButton, { backgroundColor: '#FFEB3B' }]}
              onPress={() => setTextStyle({ ...textStyle, color: '#FFEB3B' })}
            />
          </View>
          
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => setIsEditMode(false)}
            >
              <Text style={styles.modalButtonText}>{t('cancel')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.modalButton, styles.modalButtonPrimary]}
              onPress={() => setIsEditMode(false)}
            >
              <Text style={styles.modalButtonTextPrimary}>{t('done')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
  
  // Render product info modal
  const renderProductInfoModal = () => (
    <Modal
      visible={isProductInfoModalVisible}
      transparent
      animationType="slide"
      onRequestClose={() => setIsProductInfoModalVisible(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.productInfoContainer}>
          <Text style={styles.modalTitle}>{t('productDetails')}</Text>
          
          <TextInput
            style={styles.productInput}
            placeholder={t('productName')}
            placeholderTextColor="#999"
            value={productInfo.name}
            onChangeText={(text) => updateProductInfo('name', text)}
          />
          
          <TextInput
            style={styles.productInput}
            placeholder={t('price')}
            placeholderTextColor="#999"
            value={productInfo.price}
            onChangeText={(text) => updateProductInfo('price', text)}
            keyboardType="numeric"
          />
          
          <TextInput
            style={[styles.productInput, styles.productDescription]}
            placeholder={t('description')}
            placeholderTextColor="#999"
            value={productInfo.description}
            onChangeText={(text) => updateProductInfo('description', text)}
            multiline
          />
          
          <View style={styles.categoryContainer}>
            <Text style={styles.categoryLabel}>{t('category')}:</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.categoryScroll}
            >
              {['fashion', 'tech', 'home', 'beauty', 'sports', 'food'].map(
                (category) => (
                  <TouchableOpacity
                    key={category}
                    style={[
                      styles.categoryItem,
                      productInfo.category === category && styles.selectedCategory,
                    ]}
                    onPress={() => updateProductInfo('category', category)}
                  >
                    <Text
                      style={[
                        styles.categoryText,
                        productInfo.category === category &&
                          styles.selectedCategoryText,
                      ]}
                    >
                      {t(category)}
                    </Text>
                  </TouchableOpacity>
                )
              )}
            </ScrollView>
          </View>
          
          <View style={styles.tagsContainer}>
            <Text style={styles.tagsLabel}>{t('tags')}:</Text>
            <View style={styles.tagInputContainer}>
              <TextInput
                style={styles.tagInput}
                placeholder={t('addTag')}
                placeholderTextColor="#999"
                onSubmitEditing={(e) => {
                  addTag(e.nativeEvent.text);
                  (e.target as TextInput).clear();
                }}
                returnKeyType="done"
              />
            </View>
            
            <View style={styles.tagsWrapper}>
              {productInfo.tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>#{tag}</Text>
                  <TouchableOpacity
                    style={styles.removeTagButton}
                    onPress={() => removeTag(index)}
                  >
                    <Ionicons name="close-circle" size={16} color="#FFF" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>
          
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => setIsProductInfoModalVisible(false)}
            >
              <Text style={styles.modalButtonText}>{t('back')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.modalButton, styles.modalButtonPrimary]}
              onPress={() => {
                setIsProductInfoModalVisible(false);
                publishStory();
              }}
              disabled={isPublishing}
            >
              {isPublishing ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.modalButtonTextPrimary}>{t('publish')}</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
  
  return (
    <View style={styles.container}>
      {isMediaPreview ? renderMediaPreview() : renderCameraView()}
      {renderTextInputModal()}
      {renderProductInfoModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  permissionText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: '#6200ee',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cameraControls: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'space-between',
  },
  cameraTopControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
  },
  cameraBottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: 30,
  },
  cameraButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#FFFFFF',
    borderWidth: 5,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  recordingButton: {
    backgroundColor: '#FF4136',
  },
  recordingIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#FF4136',
    position: 'absolute',
    top: 25,
    left: 25,
  },
  galleryButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  flipButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  previewImage: {
    flex: 1,
    resizeMode: 'contain',
  },
  previewControls: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 20,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
  },
  previewButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersContainer: {
    position: 'absolute',
    bottom: 150,
    left: 0,
    right: 0,
    height: 80,
    paddingHorizontal: 10,
  },
  filterItem: {
    width: 60,
    height: 70,
    marginHorizontal: 5,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 10,
  },
  selectedFilter: {
    borderWidth: 2,
    borderColor: '#6200ee',
  },
  filterName: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 5,
  },
  editToolsContainer: {
    position: 'absolute',
    bottom: 80,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  editTool: {
    alignItems: 'center',
  },
  editToolText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 5,
  },
  publishButton: {
    position: 'absolute',
    bottom: 30,
    alignSelf: 'center',
    backgroundColor: '#6200ee',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 25,
  },
  publishButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  textOverlay: {
    position: 'absolute',
    padding: 5,
  },
  overlayText: {
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  textInputContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    color: '#333',
    minHeight: 100,
    textAlignVertical: 'top',
  },
  textStyleControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
  },
  textStyleButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EEE',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#DDD',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    padding: 15,
    borderRadius: 10,
    marginHorizontal: 5,
    alignItems: 'center',
    backgroundColor: '#EEE',
  },
  modalButtonPrimary: {
    backgroundColor: '#6200ee',
  },
  modalButtonText: {
    fontSize: 16,
    color: '#333',
  },
  modalButtonTextPrimary: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  productInfoContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  productInput: {
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    color: '#333',
    marginBottom: 15,
  },
  productDescription: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  categoryContainer: {
    marginBottom: 15,
  },
  categoryLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  categoryScroll: {
    flexDirection: 'row',
  },
  categoryItem: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#EEE',
    marginRight: 10,
  },
  selectedCategory: {
    backgroundColor: '#6200ee',
  },
  categoryText: {
    color: '#333',
  },
  selectedCategoryText: {
    color: '#FFFFFF',
  },
  tagsContainer: {
    marginBottom: 15,
  },
  tagsLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  tagInputContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  tagInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 10,
    padding: 10,
    fontSize: 16,
    color: '#333',
  },
  tagsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#6200ee',
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
    margin: 5,
  },
  tagText: {
    color: '#FFFFFF',
    marginRight: 5,
  },
  removeTagButton: {
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CameraScreen;