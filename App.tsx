import React, { useEffect } from 'react';
import { <PERSON>Bar, LogBox, I18nManager } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { store } from './src/store';
import AppNavigator from './src/navigation/AppNavigator';
import { useSelector } from 'react-redux';
import { RootState } from './src/store';
import { useTranslation } from './src/i18n';

// Ignore specific warnings
LogBox.ignoreLogs([
  'ViewPropTypes will be removed',
  'ColorPropType will be removed',
]);

// App wrapper for Redux Provider
const App = () => {
  return (
    <Provider store={store}>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <AppContent />
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </Provider>
  );
};

// App content with access to Redux state
const AppContent = () => {
  const theme = useSelector((state: RootState) => state.theme);
  const language = useSelector((state: RootState) => state.language);
  const { t, changeLanguage } = useTranslation();

  // Set up RTL layout for Arabic and other RTL languages
  useEffect(() => {
    const setupRTL = async () => {
      try {
        // Set the language and RTL direction
        await changeLanguage(language.currentLanguage);
        
        // Force RTL layout for RTL languages
        if ((language as { isRTL: boolean }).isRTL !== I18nManager.isRTL) {
          I18nManager.forceRTL(language.isRTL);
          // In a real app, you might want to reload the app here
          // to ensure RTL layout is applied correctly
        }
      } catch (error) {
        console.error('Failed to set up RTL:', error);
      }
    };

    setupRTL();
  }, [language.currentLanguage, language.isRTL]);

  return (
    <NavigationContainer
      theme={{
        dark: theme.mode === 'dark',
        colors: {
          primary: theme.theme.colors.primary,
          background: theme.theme.colors.background,
          card: theme.theme.colors.surface,
          text: theme.theme.colors.text,
          border: theme.theme.colors.outline || '#e0e0e0',
          notification: theme.theme.colors.error,
        },
      }}
    >
      <StatusBar
        barStyle={theme.mode === 'dark' ? 'light-content' : 'dark-content'}
        backgroundColor={theme.theme.colors.background}
      />
      <AppNavigator />
    </NavigationContainer>
  );
};
export default App;
