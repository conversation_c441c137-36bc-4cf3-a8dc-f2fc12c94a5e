import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { Provider } from 'react-redux';
import { store } from './src/store';

// Simple working app
const SimpleApp = () => {
  const [count, setCount] = React.useState(0);
  const [darkMode, setDarkMode] = React.useState(false);

  const backgroundColor = darkMode ? '#121212' : '#f5f5f5';
  const textColor = darkMode ? '#ffffff' : '#000000';
  const cardColor = darkMode ? '#1e1e1e' : '#ffffff';

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <View style={[styles.header, { backgroundColor: cardColor }]}>
        <Text style={[styles.title, { color: textColor }]}>
          🛍️ SaleStory
        </Text>
        <Text style={[styles.subtitle, { color: textColor }]}>
          قصص البيع
        </Text>
      </View>

      <View style={[styles.content, { backgroundColor: cardColor }]}>
        <Text style={[styles.welcomeText, { color: textColor }]}>
          مرحباً بك في تطبيق SaleStory! 🎉
        </Text>
        <Text style={[styles.description, { color: textColor }]}>
          Welcome to SaleStory App!
        </Text>

        <View style={styles.counterSection}>
          <Text style={[styles.counterText, { color: textColor }]}>
            العداد: {count}
          </Text>
          <TouchableOpacity
            style={styles.button}
            onPress={() => setCount(count + 1)}
          >
            <Text style={styles.buttonText}>اضغط هنا +</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={[styles.themeButton, { backgroundColor: darkMode ? '#6200ee' : '#333' }]}
          onPress={() => setDarkMode(!darkMode)}
        >
          <Text style={styles.buttonText}>
            {darkMode ? '☀️ الوضع الفاتح' : '🌙 الوضع الداكن'}
          </Text>
        </TouchableOpacity>

        <View style={styles.infoSection}>
          <Text style={[styles.infoText, { color: textColor }]}>
            📱 Platform: {Platform.OS}
          </Text>
          <Text style={[styles.infoText, { color: textColor }]}>
            ✅ React Native يعمل بنجاح
          </Text>
          <Text style={[styles.infoText, { color: textColor }]}>
            ✅ Redux Store متصل
          </Text>
          <Text style={[styles.infoText, { color: textColor }]}>
            ✅ دعم العربية والإنجليزية
          </Text>
        </View>
      </View>
    </View>
  );
};

const App = () => {
  return (
    <Provider store={store}>
      <SimpleApp />
    </Provider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: Platform.OS === 'web' ? 20 : 50,
  },
  header: {
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 18,
    opacity: 0.8,
  },
  content: {
    flex: 1,
    padding: 20,
    borderRadius: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    opacity: 0.8,
  },
  counterSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  counterText: {
    fontSize: 20,
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#6200ee',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    elevation: 2,
  },
  themeButton: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    alignItems: 'center',
    marginBottom: 30,
    elevation: 2,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoSection: {
    marginTop: 20,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 8,
    textAlign: 'center',
  },
});

export default App;
