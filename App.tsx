import React, { useEffect } from 'react';
import { StatusBar, LogBox, I18nManager, Platform } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { PaperProvider } from 'react-native-paper';
import { store } from './src/store';
import AppNavigator from './src/navigation/AppNavigator';
import { useSelector } from 'react-redux';
import { RootState } from './src/store';
import { useTranslation } from './src/i18n';
import { lightTheme, darkTheme } from './src/store/slices/themeSlice';

// Ignore specific warnings
LogBox.ignoreLogs([
  'ViewPropTypes will be removed',
  'ColorPropType will be removed',
  'Non-serializable values were found in the navigation state',
]);

// App wrapper for Redux Provider
const App = () => {
  return (
    <Provider store={store}>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <AppContent />
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </Provider>
  );
};

// App content with access to Redux state
const AppContent = () => {
  const theme = useSelector((state: RootState) => state.theme);
  const language = useSelector((state: RootState) => state.language);
  const { t, changeLanguage } = useTranslation();

  // Set up RTL layout for Arabic and other RTL languages
  useEffect(() => {
    const setupRTL = async () => {
      try {
        // Set the language and RTL direction
        await changeLanguage(language.currentLanguage);

        // Force RTL layout for RTL languages (only on native platforms)
        if (Platform.OS !== 'web' && language.isRTL !== I18nManager.isRTL) {
          I18nManager.forceRTL(language.isRTL);
        }
      } catch (error) {
        console.error('Failed to set up RTL:', error);
      }
    };

    setupRTL();
  }, [language.currentLanguage, language.isRTL]);

  const currentTheme = theme.mode === 'dark' ? darkTheme : lightTheme;

  return (
    <PaperProvider theme={currentTheme}>
      <NavigationContainer
        theme={{
          dark: theme.mode === 'dark',
          colors: {
            primary: currentTheme.colors.primary,
            background: currentTheme.colors.background,
            card: currentTheme.colors.surface,
            text: currentTheme.colors.onSurface,
            border: currentTheme.colors.outline,
            notification: currentTheme.colors.error,
          },
        }}
      >
        <StatusBar
          barStyle={theme.mode === 'dark' ? 'light-content' : 'dark-content'}
          backgroundColor={currentTheme.colors.surface}
        />
        <AppNavigator />
      </NavigationContainer>
    </PaperProvider>
  );
};

export default App;
