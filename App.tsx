import React from 'react';
import { Provider } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { store } from './src/store';
import SimpleHomeScreen from './src/screens/SimpleHomeScreen';

// Simple app with Redux and Navigation
const AppContent = () => {
  return (
    <NavigationContainer>
      <SimpleHomeScreen />
    </NavigationContainer>
  );
};

const App = () => {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
};



export default App;
