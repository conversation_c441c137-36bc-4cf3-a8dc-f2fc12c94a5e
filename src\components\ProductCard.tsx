import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from '../i18n';
import { Story } from '../store/slices/storySlice';
import { useDispatch } from 'react-redux';
import { likeStory, saveStory } from '../store/slices/storySlice';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 40) / 2; // 2 columns with padding

interface ProductCardProps {
  story: Story;
  onPress: () => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ story, onPress }) => {
  const { t, isRTL } = useTranslation();
  const dispatch = useDispatch();

  const handleLike = () => {
    dispatch(likeStory(story.id));
  };

  const handleSave = () => {
    dispatch(saveStory(story.id));
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress} activeOpacity={0.8}>
      <View style={styles.card}>
        {/* Product Image */}
        <Image 
          source={{ uri: story.media[0].url }} 
          style={styles.image} 
          resizeMode="cover" 
        />
        
        {/* Video Indicator */}
        {story.media[0].type === 'video' && (
          <View style={styles.videoIndicator}>
            <Ionicons name="play-circle" size={24} color="white" />
          </View>
        )}

        {/* Action Buttons */}
        <View style={[styles.actionButtons, isRTL ? styles.actionButtonsRTL : {}]}>
          <TouchableOpacity style={styles.actionButton} onPress={handleLike}>
            <Ionicons 
              name={story.liked ? 'heart' : 'heart-outline'} 
              size={20} 
              color={story.liked ? '#ff4081' : 'white'} 
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleSave}>
            <Ionicons 
              name={story.saved ? 'bookmark' : 'bookmark-outline'} 
              size={20} 
              color={story.saved ? '#6200ee' : 'white'} 
            />
          </TouchableOpacity>
        </View>

        {/* Product Info */}
        <View style={styles.infoContainer}>
          <Text style={styles.productName} numberOfLines={1}>
            {story.product.name}
          </Text>
          <Text style={styles.price}>
            {story.product.price} {story.product.currency}
          </Text>
          <View style={styles.userContainer}>
            <Image source={{ uri: story.userAvatar }} style={styles.userAvatar} />
            <Text style={styles.username} numberOfLines={1}>
              {story.username}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: CARD_WIDTH,
    margin: 8,
  },
  card: {
    borderRadius: 12,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: CARD_WIDTH * 1.2,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  videoIndicator: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 15,
    padding: 5,
  },
  actionButtons: {
    position: 'absolute',
    top: 10,
    left: 10,
    flexDirection: 'column',
  },
  actionButtonsRTL: {
    left: undefined,
    right: 10,
  },
  actionButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 15,
    padding: 5,
    marginBottom: 8,
  },
  infoContainer: {
    padding: 10,
  },
  productName: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  price: {
    fontSize: 14,
    color: '#6200ee',
    fontWeight: 'bold',
    marginBottom: 8,
  },
  userContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userAvatar: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 5,
  },
  username: {
    fontSize: 12,
    color: '#666',
    flex: 1,
  },
});

export default ProductCard;