import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Story {
  id: string;
  userId: string;
  username: string;
  userAvatar: string;
  media: {
    type: 'image' | 'video';
    url: string;
    thumbnail?: string;
  }[];
  product: {
    id: string;
    name: string;
    price: number;
    currency: string;
    description: string;
    category: string;
  };
  likes: number;
  views: number;
  shares: number;
  saved: boolean;
  liked: boolean;
  createdAt: string;
  location?: string;
  tags?: string[];
}

interface StoryState {
  stories: Story[];
  currentStory: Story | null;
  loading: boolean;
  error: string | null;
  filters: {
    category: string | null;
    priceRange: [number, number] | null;
    location: string | null;
  };
  savedStories: string[];
}

const initialState: StoryState = {
  stories: [],
  currentStory: null,
  loading: false,
  error: null,
  filters: {
    category: null,
    priceRange: null,
    location: null,
  },
  savedStories: [],
};

const storySlice = createSlice({
  name: 'stories',
  initialState,
  reducers: {
    fetchStoriesStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchStoriesSuccess: (state, action: PayloadAction<Story[]>) => {
      state.stories = action.payload;
      state.loading = false;
    },
    fetchStoriesFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    setCurrentStory: (state, action: PayloadAction<Story>) => {
      state.currentStory = action.payload;
    },
    clearCurrentStory: (state) => {
      state.currentStory = null;
    },
    likeStory: (state, action: PayloadAction<string>) => {
      const storyId = action.payload;
      const story = state.stories.find((s) => s.id === storyId);
      if (story) {
        story.liked = !story.liked;
        story.likes = story.liked ? story.likes + 1 : story.likes - 1;
      }
      if (state.currentStory?.id === storyId) {
        state.currentStory.liked = !state.currentStory.liked;
        state.currentStory.likes = state.currentStory.liked
          ? state.currentStory.likes + 1
          : state.currentStory.likes - 1;
      }
    },
    saveStory: (state, action: PayloadAction<string>) => {
      const storyId = action.payload;
      const storyIndex = state.savedStories.indexOf(storyId);
      
      if (storyIndex === -1) {
        state.savedStories.push(storyId);
        const story = state.stories.find((s) => s.id === storyId);
        if (story) {
          story.saved = true;
        }
        if (state.currentStory?.id === storyId) {
          state.currentStory.saved = true;
        }
      } else {
        state.savedStories.splice(storyIndex, 1);
        const story = state.stories.find((s) => s.id === storyId);
        if (story) {
          story.saved = false;
        }
        if (state.currentStory?.id === storyId) {
          state.currentStory.saved = false;
        }
      }
    },
    incrementViews: (state, action: PayloadAction<string>) => {
      const storyId = action.payload;
      const story = state.stories.find((s) => s.id === storyId);
      if (story) {
        story.views += 1;
      }
      if (state.currentStory?.id === storyId) {
        state.currentStory.views += 1;
      }
    },
    shareStory: (state, action: PayloadAction<string>) => {
      const storyId = action.payload;
      const story = state.stories.find((s) => s.id === storyId);
      if (story) {
        story.shares += 1;
      }
      if (state.currentStory?.id === storyId) {
        state.currentStory.shares += 1;
      }
    },
    setFilters: (state, action: PayloadAction<Partial<StoryState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
  },
});

export const {
  fetchStoriesStart,
  fetchStoriesSuccess,
  fetchStoriesFailure,
  setCurrentStory,
  clearCurrentStory,
  likeStory,
  saveStory,
  incrementViews,
  shareStory,
  setFilters,
  clearFilters,
} = storySlice.actions;

export default storySlice.reducer;