import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import {
  Searchbar,
  Surface,
  Badge,
  IconButton,
} from 'react-native-paper';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { useTranslation } from '../i18n';

// Mock chat data
const mockChats = [
  {
    id: '1',
    name: '<PERSON>',
    nameAr: 'سارة جونسون',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
    lastMessage: 'Is this item still available?',
    lastMessageAr: 'هل هذا المنتج ما زال متوفراً؟',
    timestamp: '2 min ago',
    timestampAr: 'منذ دقيقتين',
    unreadCount: 2,
    isOnline: true,
    productImage: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=100',
  },
  {
    id: '2',
    name: '<PERSON>',
    nameAr: 'أحمد علي',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
    lastMessage: 'Thank you for the quick delivery!',
    lastMessageAr: 'شكراً لك على التوصيل السريع!',
    timestamp: '1 hour ago',
    timestampAr: 'منذ ساعة',
    unreadCount: 0,
    isOnline: false,
    productImage: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100',
  },
  {
    id: '3',
    name: 'Maria Garcia',
    nameAr: 'ماريا غارسيا',
    avatar: 'https://randomuser.me/api/portraits/women/3.jpg',
    lastMessage: 'Can you send more photos?',
    lastMessageAr: 'هل يمكنك إرسال المزيد من الصور؟',
    timestamp: '3 hours ago',
    timestampAr: 'منذ 3 ساعات',
    unreadCount: 1,
    isOnline: true,
    productImage: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=100',
  },
  {
    id: '4',
    name: 'John Smith',
    nameAr: 'جون سميث',
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
    lastMessage: 'Great product, highly recommended!',
    lastMessageAr: 'منتج رائع، أنصح به بشدة!',
    timestamp: '1 day ago',
    timestampAr: 'منذ يوم',
    unreadCount: 0,
    isOnline: false,
    productImage: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=100',
  },
  {
    id: '5',
    name: 'Lisa Chen',
    nameAr: 'ليزا تشين',
    avatar: 'https://randomuser.me/api/portraits/women/5.jpg',
    lastMessage: 'What are the shipping options?',
    lastMessageAr: 'ما هي خيارات الشحن؟',
    timestamp: '2 days ago',
    timestampAr: 'منذ يومين',
    unreadCount: 0,
    isOnline: true,
    productImage: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=100',
  },
];

const NewChatScreen = () => {
  const theme = useSelector((state: RootState) => state.theme);
  const language = useSelector((state: RootState) => state.language);
  const { t } = useTranslation();

  const [searchQuery, setSearchQuery] = useState('');

  const filteredChats = mockChats.filter(chat => {
    const name = language.currentLanguage === 'ar' ? chat.nameAr : chat.name;
    const message = language.currentLanguage === 'ar' ? chat.lastMessageAr : chat.lastMessage;
    return name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           message.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const renderChatItem = ({ item }: { item: any }) => (
    <TouchableOpacity style={[styles.chatItem, { backgroundColor: theme.theme.colors.surface }]}>
      <View style={styles.avatarContainer}>
        <Image source={{ uri: item.avatar }} style={styles.avatar} />
        {item.isOnline && <View style={[styles.onlineIndicator, { backgroundColor: '#4CAF50' }]} />}
      </View>

      <View style={styles.chatContent}>
        <View style={styles.chatHeader}>
          <Text style={[styles.chatName, { color: theme.theme.colors.onSurface }]}>
            {language.currentLanguage === 'ar' ? item.nameAr : item.name}
          </Text>
          <Text style={[styles.timestamp, { color: theme.theme.colors.onSurfaceVariant }]}>
            {language.currentLanguage === 'ar' ? item.timestampAr : item.timestamp}
          </Text>
        </View>

        <View style={styles.messageContainer}>
          <Text 
            style={[
              styles.lastMessage, 
              { color: theme.theme.colors.onSurfaceVariant },
              item.unreadCount > 0 && { fontWeight: 'bold', color: theme.theme.colors.onSurface }
            ]}
            numberOfLines={1}
          >
            {language.currentLanguage === 'ar' ? item.lastMessageAr : item.lastMessage}
          </Text>
          
          <View style={styles.messageActions}>
            {item.productImage && (
              <Image source={{ uri: item.productImage }} style={styles.productThumbnail} />
            )}
            {item.unreadCount > 0 && (
              <Badge style={[styles.unreadBadge, { backgroundColor: theme.theme.colors.primary }]}>
                {item.unreadCount}
              </Badge>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.theme.colors.background }]}>
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.theme.colors.surface }]}>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.theme.colors.onSurface }]}>
            {t('chat')}
          </Text>
          <IconButton
            icon="account-plus"
            size={24}
            iconColor={theme.theme.colors.primary}
            onPress={() => {
              // Handle new chat
            }}
          />
        </View>
        
        <Searchbar
          placeholder={language.currentLanguage === 'ar' ? 'ابحث في المحادثات...' : 'Search conversations...'}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />
      </Surface>

      {/* Chat List */}
      <FlatList
        data={filteredChats}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.chatList}
      />

      {/* Empty State */}
      {filteredChats.length === 0 && (
        <View style={styles.emptyState}>
          <Text style={[styles.emptyStateIcon, { color: theme.theme.colors.onSurfaceVariant }]}>
            💬
          </Text>
          <Text style={[styles.emptyStateText, { color: theme.theme.colors.onSurfaceVariant }]}>
            {searchQuery 
              ? (language.currentLanguage === 'ar' ? 'لا توجد محادثات مطابقة' : 'No matching conversations')
              : (language.currentLanguage === 'ar' ? 'لا توجد محادثات بعد' : 'No conversations yet')
            }
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: Platform.OS === 'web' ? 20 : 50,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  searchBar: {
    elevation: 0,
  },
  chatList: {
    padding: 10,
  },
  chatItem: {
    flexDirection: 'row',
    padding: 15,
    marginVertical: 5,
    marginHorizontal: 10,
    borderRadius: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 15,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: 'white',
  },
  chatContent: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  chatName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  timestamp: {
    fontSize: 12,
  },
  messageContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    fontSize: 14,
    flex: 1,
    marginRight: 10,
  },
  messageActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productThumbnail: {
    width: 30,
    height: 30,
    borderRadius: 5,
    marginRight: 5,
  },
  unreadBadge: {
    minWidth: 20,
    height: 20,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyStateIcon: {
    fontSize: 64,
    marginBottom: 20,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default NewChatScreen;
