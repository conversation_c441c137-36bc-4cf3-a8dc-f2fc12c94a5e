import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { DefaultTheme, DarkTheme } from '@react-navigation/native';
import { MD3DarkTheme, MD3LightTheme } from 'react-native-paper';

// Define custom colors for our app
const customColors = {
  primary: '#6200ee',
  onPrimary: '#ffffff',
  primaryContainer: '#eaddff',
  onPrimaryContainer: '#21005d',
  secondary: '#03dac6',
  onSecondary: '#000000',
  secondaryContainer: '#ccf8f3',
  onSecondaryContainer: '#002e2a',
  tertiary: '#ff4081',
  onTertiary: '#ffffff',
  tertiaryContainer: '#ffd8e4',
  onTertiaryContainer: '#31111d',
  error: '#b00020',
  onError: '#ffffff',
  errorContainer: '#ffdad6',
  onErrorContainer: '#410002',
  background: '#f6f6f6',
  onBackground: '#1c1b1f',
  surface: '#ffffff',
  onSurface: '#1c1b1f',
  surfaceVariant: '#e7e0ec',
  onSurfaceVariant: '#49454f',
  outline: '#79747e',
  outlineVariant: '#cab6d6',
  shadow: '#000000',
  scrim: '#000000',
  inverseSurface: '#313033',
  inverseOnSurface: '#f4eff4',
  inversePrimary: '#d0bcff',
  elevation: {
    level0: 'transparent',
    level1: '#f7f2fa',
    level2: '#f2edf4',
    level3: '#ede8f0',
    level4: '#ebe6ec',
    level5: '#e6e1e8',
  },
  surfaceDisabled: 'rgba(28, 27, 31, 0.12)',
  onSurfaceDisabled: 'rgba(28, 27, 31, 0.38)',
  backdrop: 'rgba(73, 69, 79, 0.4)',
};

// Define custom dark colors
const customDarkColors = {
  primary: '#d0bcff',
  onPrimary: '#381e72',
  primaryContainer: '#4f378b',
  onPrimaryContainer: '#eaddff',
  secondary: '#03dac6',
  onSecondary: '#003734',
  secondaryContainer: '#004e47',
  onSecondaryContainer: '#ccf8f3',
  tertiary: '#efb8c8',
  onTertiary: '#492532',
  tertiaryContainer: '#633b48',
  onTertiaryContainer: '#ffd8e4',
  error: '#ffb4ab',
  onError: '#690005',
  errorContainer: '#93000a',
  onErrorContainer: '#ffdad6',
  background: '#121212',
  onBackground: '#e6e1e5',
  surface: '#1e1e1e',
  onSurface: '#e6e1e5',
  surfaceVariant: '#49454f',
  onSurfaceVariant: '#cab6d6',
  outline: '#938f96',
  outlineVariant: '#49454f',
  shadow: '#000000',
  scrim: '#000000',
  inverseSurface: '#e6e1e5',
  inverseOnSurface: '#313033',
  inversePrimary: '#6750a4',
  elevation: {
    level0: 'transparent',
    level1: '#22202a',
    level2: '#28253a',
    level3: '#2d2a4a',
    level4: '#2f2c4c',
    level5: '#322f56',
  },
  surfaceDisabled: 'rgba(230, 225, 229, 0.12)',
  onSurfaceDisabled: 'rgba(230, 225, 229, 0.38)',
  backdrop: 'rgba(73, 69, 79, 0.4)',
};

// Create custom light theme
const customLightTheme = {
  ...DefaultTheme,
  ...MD3LightTheme,
  colors: {
    ...DefaultTheme.colors,
    ...MD3LightTheme.colors,
    ...customColors,
  },
};

// Create custom dark theme
const customDarkTheme = {
  ...DarkTheme,
  ...MD3DarkTheme,
  colors: {
    ...DarkTheme.colors,
    ...MD3DarkTheme.colors,
    ...customDarkColors,
  },
};

type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeState {
  mode: ThemeMode;
  theme: typeof customLightTheme | typeof customDarkTheme;
  systemIsDark: boolean;
}

const initialState: ThemeState = {
  mode: 'system',
  theme: customLightTheme,
  systemIsDark: false,
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setThemeMode: (state, action: PayloadAction<ThemeMode>) => {
      state.mode = action.payload;
      
      if (action.payload === 'light') {
        state.theme = customLightTheme;
      } else if (action.payload === 'dark') {
        state.theme = customDarkTheme;
      } else if (action.payload === 'system') {
        state.theme = state.systemIsDark ? customDarkTheme : customLightTheme;
      }
    },
    setSystemIsDark: (state, action: PayloadAction<boolean>) => {
      state.systemIsDark = action.payload;
      
      if (state.mode === 'system') {
        state.theme = action.payload ? customDarkTheme : customLightTheme;
      }
    },
  },
});

export const { setThemeMode, setSystemIsDark } = themeSlice.actions;

export const lightTheme = customLightTheme;
export const darkTheme = customDarkTheme;

export default themeSlice.reducer;