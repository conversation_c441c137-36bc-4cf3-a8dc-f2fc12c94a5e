import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { DefaultTheme, DarkTheme } from '@react-navigation/native';
import { MD3DarkTheme, MD3LightTheme } from 'react-native-paper';

// Define custom colors for our app
const customColors = {
  primary: '#6200ee',
  secondary: '#03dac6',
  accent: '#ff4081',
  background: '#f6f6f6',
  surface: '#ffffff',
  error: '#b00020',
  text: '#000000',
  disabled: '#9e9e9e',
  placeholder: '#9e9e9e',
  backdrop: 'rgba(0, 0, 0, 0.5)',
  notification: '#ff80ab',
};

// Define custom dark colors
const customDarkColors = {
  primary: '#bb86fc',
  secondary: '#03dac6',
  accent: '#ff4081',
  background: '#121212',
  surface: '#1e1e1e',
  error: '#cf6679',
  text: '#ffffff',
  disabled: '#6e6e6e',
  placeholder: '#6e6e6e',
  backdrop: 'rgba(0, 0, 0, 0.5)',
  notification: '#ff80ab',
};

// Create custom light theme
const customLightTheme = {
  ...DefaultTheme,
  ...MD3LightTheme,
  colors: {
    ...DefaultTheme.colors,
    ...MD3LightTheme.colors,
    ...customColors,
  },
};

// Create custom dark theme
const customDarkTheme = {
  ...DarkTheme,
  ...MD3DarkTheme,
  colors: {
    ...DarkTheme.colors,
    ...MD3DarkTheme.colors,
    ...customDarkColors,
  },
};

type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeState {
  mode: ThemeMode;
  theme: typeof customLightTheme | typeof customDarkTheme;
  systemIsDark: boolean;
}

const initialState: ThemeState = {
  mode: 'system',
  theme: customLightTheme,
  systemIsDark: false,
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setThemeMode: (state, action: PayloadAction<ThemeMode>) => {
      state.mode = action.payload;
      
      if (action.payload === 'light') {
        state.theme = customLightTheme;
      } else if (action.payload === 'dark') {
        state.theme = customDarkTheme;
      } else if (action.payload === 'system') {
        state.theme = state.systemIsDark ? customDarkTheme : customLightTheme;
      }
    },
    setSystemIsDark: (state, action: PayloadAction<boolean>) => {
      state.systemIsDark = action.payload;
      
      if (state.mode === 'system') {
        state.theme = action.payload ? customDarkTheme : customLightTheme;
      }
    },
  },
});

export const { setThemeMode, setSystemIsDark } = themeSlice.actions;

export const lightTheme = customLightTheme;
export const darkTheme = customDarkTheme;

export default themeSlice.reducer;