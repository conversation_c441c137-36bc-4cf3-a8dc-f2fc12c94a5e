import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { useTranslation } from '../i18n';

// Auth Screens
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import WelcomeScreen from '../screens/auth/WelcomeScreen';

// Main Screens
import HomeScreen from '../screens/HomeScreen';
import NewExploreScreen from '../screens/NewExploreScreen';
import NewCameraScreen from '../screens/NewCameraScreen';
import NewChatScreen from '../screens/NewChatScreen';
import NewProfileScreen from '../screens/NewProfileScreen';

// Additional Screens
import ProductDetailScreen from '../screens/ProductDetailScreen';
import CreateStoryScreen from '../screens/CreateStoryScreen';
import SettingsScreen from '../screens/SettingsScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import SearchScreen from '../screens/SearchScreen';

// Stack Navigators
const AuthStack = createStackNavigator();
const MainStack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Auth Navigator
const AuthNavigator = () => {
  return (
    <AuthStack.Navigator screenOptions={{ headerShown: false }}>
      <AuthStack.Screen name="Welcome" component={WelcomeScreen} />
      <AuthStack.Screen name="Login" component={LoginScreen} />
      <AuthStack.Screen name="Register" component={RegisterScreen} />
    </AuthStack.Navigator>
  );
};

// Tab Navigator
const TabNavigator = () => {
  const { t } = useTranslation();
  const theme = useSelector((state: RootState) => state.theme);

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'HomeTab') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'ExploreTab') {
            iconName = focused ? 'search' : 'search-outline';
          } else if (route.name === 'CameraTab') {
            iconName = focused ? 'add-circle' : 'add-circle-outline';
          } else if (route.name === 'ChatTab') {
            iconName = focused ? 'chatbubble' : 'chatbubble-outline';
          } else if (route.name === 'ProfileTab') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.theme.colors.primary,
        tabBarInactiveTintColor: theme.theme.colors.onSurfaceVariant,
        tabBarStyle: {
          backgroundColor: theme.theme.colors.surface,
          borderTopColor: theme.theme.colors.outline,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{ tabBarLabel: t('home') }}
      />
      <Tab.Screen
        name="ExploreTab"
        component={NewExploreScreen}
        options={{ tabBarLabel: t('explore') }}
      />
      <Tab.Screen
        name="CameraTab"
        component={NewCameraScreen}
        options={{ tabBarLabel: t('create') }}
      />
      <Tab.Screen
        name="ChatTab"
        component={NewChatScreen}
        options={{ tabBarLabel: t('chat') }}
      />
      <Tab.Screen
        name="ProfileTab"
        component={NewProfileScreen}
        options={{ tabBarLabel: t('profile') }}
      />
    </Tab.Navigator>
  );
};

// Main Navigator
const MainNavigator = () => {
  return (
    <MainStack.Navigator screenOptions={{ headerShown: false }}>
      <MainStack.Screen name="Tabs" component={TabNavigator} />
      <MainStack.Screen name="ProductDetail" component={ProductDetailScreen} />
      <MainStack.Screen name="CreateStory" component={CreateStoryScreen} />
      <MainStack.Screen name="Settings" component={SettingsScreen} />
      <MainStack.Screen name="Notifications" component={NotificationsScreen} />
      <MainStack.Screen name="Search" component={SearchScreen} />
    </MainStack.Navigator>
  );
};

// Root Navigator
const AppNavigator = () => {
  const user = useSelector((state: RootState) => state.user);

  // Show auth screens if user is not logged in
  if (!user.isAuthenticated) {
    return <AuthNavigator />;
  }

  // Show main app if user is logged in
  return <MainNavigator />;
};

export default AppNavigator;