import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Platform,
  Alert,
} from 'react-native';
import {
  Surface,
  Button,
  IconButton,
  Chip,
  Divider,
} from 'react-native-paper';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { useTranslation } from '../i18n';
import { useNavigation } from '@react-navigation/native';

const ProductDetailScreen = () => {
  const theme = useSelector((state: RootState) => state.theme);
  const language = useSelector((state: RootState) => state.language);
  const { t } = useTranslation();
  const navigation = useNavigation();

  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Mock product data
  const product = {
    id: '1',
    name: 'Wireless Bluetooth Headphones',
    nameAr: 'سماعات بلوتوث لاسلكية',
    price: 99.99,
    originalPrice: 149.99,
    description: 'High-quality wireless headphones with noise cancellation and 30-hour battery life.',
    descriptionAr: 'سماعات لاسلكية عالية الجودة مع إلغاء الضوضاء وبطارية تدوم 30 ساعة.',
    images: [
      'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',
      'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=400',
      'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400',
    ],
    category: 'Electronics',
    categoryAr: 'إلكترونيات',
    rating: 4.5,
    reviewCount: 128,
    seller: {
      name: 'TechStore',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      rating: 4.8,
      isVerified: true,
    },
    features: [
      { en: 'Noise Cancellation', ar: 'إلغاء الضوضاء' },
      { en: '30-hour Battery', ar: 'بطارية 30 ساعة' },
      { en: 'Wireless Charging', ar: 'شحن لاسلكي' },
      { en: 'Water Resistant', ar: 'مقاوم للماء' },
    ],
    inStock: true,
    stockCount: 15,
  };

  const handleAddToCart = () => {
    Alert.alert(
      t('success'),
      language.currentLanguage === 'ar' ? 'تم إضافة المنتج للسلة' : 'Product added to cart'
    );
  };

  const handleBuyNow = () => {
    Alert.alert(
      t('comingSoon'),
      language.currentLanguage === 'ar' ? 'ميزة الشراء قريباً' : 'Purchase feature coming soon'
    );
  };

  const handleContactSeller = () => {
    Alert.alert(
      t('comingSoon'),
      language.currentLanguage === 'ar' ? 'ميزة التواصل مع البائع قريباً' : 'Contact seller feature coming soon'
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.theme.colors.background }]}>
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.theme.colors.surface }]}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => navigation.goBack()}
          iconColor={theme.theme.colors.onSurface}
        />
        <Text style={[styles.headerTitle, { color: theme.theme.colors.onSurface }]}>
          {language.currentLanguage === 'ar' ? 'تفاصيل المنتج' : 'Product Details'}
        </Text>
        <IconButton
          icon={isFavorite ? 'heart' : 'heart-outline'}
          size={24}
          onPress={() => setIsFavorite(!isFavorite)}
          iconColor={isFavorite ? theme.theme.colors.error : theme.theme.colors.onSurface}
        />
      </Surface>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Product Images */}
        <View style={styles.imageSection}>
          <Image 
            source={{ uri: product.images[selectedImageIndex] }} 
            style={styles.mainImage} 
          />
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.imageSelector}
          >
            {product.images.map((image, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => setSelectedImageIndex(index)}
                style={[
                  styles.thumbnailContainer,
                  selectedImageIndex === index && styles.selectedThumbnail
                ]}
              >
                <Image source={{ uri: image }} style={styles.thumbnail} />
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Product Info */}
        <Surface style={[styles.infoSection, { backgroundColor: theme.theme.colors.surface }]}>
          <View style={styles.titleRow}>
            <View style={styles.titleContainer}>
              <Text style={[styles.productName, { color: theme.theme.colors.onSurface }]}>
                {language.currentLanguage === 'ar' ? product.nameAr : product.name}
              </Text>
              <Chip style={styles.categoryChip}>
                {language.currentLanguage === 'ar' ? product.categoryAr : product.category}
              </Chip>
            </View>
          </View>

          <View style={styles.priceRow}>
            <Text style={[styles.price, { color: theme.theme.colors.primary }]}>
              ${product.price}
            </Text>
            <Text style={[styles.originalPrice, { color: theme.theme.colors.onSurfaceVariant }]}>
              ${product.originalPrice}
            </Text>
            <Chip style={styles.discountChip}>
              {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
            </Chip>
          </View>

          <View style={styles.ratingRow}>
            <Text style={styles.rating}>⭐ {product.rating}</Text>
            <Text style={[styles.reviewCount, { color: theme.theme.colors.onSurfaceVariant }]}>
              ({product.reviewCount} {language.currentLanguage === 'ar' ? 'تقييم' : 'reviews'})
            </Text>
          </View>

          <Text style={[styles.description, { color: theme.theme.colors.onSurfaceVariant }]}>
            {language.currentLanguage === 'ar' ? product.descriptionAr : product.description}
          </Text>

          {/* Features */}
          <View style={styles.featuresSection}>
            <Text style={[styles.featuresTitle, { color: theme.theme.colors.onSurface }]}>
              {language.currentLanguage === 'ar' ? 'المميزات' : 'Features'}
            </Text>
            {product.features.map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <Text style={styles.featureIcon}>✓</Text>
                <Text style={[styles.featureText, { color: theme.theme.colors.onSurfaceVariant }]}>
                  {language.currentLanguage === 'ar' ? feature.ar : feature.en}
                </Text>
              </View>
            ))}
          </View>

          <Divider style={styles.divider} />

          {/* Seller Info */}
          <View style={styles.sellerSection}>
            <Text style={[styles.sellerTitle, { color: theme.theme.colors.onSurface }]}>
              {language.currentLanguage === 'ar' ? 'البائع' : 'Seller'}
            </Text>
            <View style={styles.sellerInfo}>
              <Image source={{ uri: product.seller.avatar }} style={styles.sellerAvatar} />
              <View style={styles.sellerDetails}>
                <View style={styles.sellerNameRow}>
                  <Text style={[styles.sellerName, { color: theme.theme.colors.onSurface }]}>
                    {product.seller.name}
                  </Text>
                  {product.seller.isVerified && (
                    <Text style={styles.verifiedBadge}>✓</Text>
                  )}
                </View>
                <Text style={[styles.sellerRating, { color: theme.theme.colors.onSurfaceVariant }]}>
                  ⭐ {product.seller.rating} {language.currentLanguage === 'ar' ? 'تقييم البائع' : 'seller rating'}
                </Text>
              </View>
              <Button
                mode="outlined"
                onPress={handleContactSeller}
                compact
              >
                {language.currentLanguage === 'ar' ? 'تواصل' : 'Contact'}
              </Button>
            </View>
          </View>

          {/* Stock Info */}
          <View style={styles.stockSection}>
            <Text style={[styles.stockText, { color: product.inStock ? '#4CAF50' : theme.theme.colors.error }]}>
              {product.inStock 
                ? `✓ ${language.currentLanguage === 'ar' ? 'متوفر' : 'In Stock'} (${product.stockCount} ${language.currentLanguage === 'ar' ? 'قطعة' : 'items'})`
                : `✗ ${language.currentLanguage === 'ar' ? 'نفد المخزون' : 'Out of Stock'}`
              }
            </Text>
          </View>
        </Surface>
      </ScrollView>

      {/* Action Buttons */}
      <Surface style={[styles.actionBar, { backgroundColor: theme.theme.colors.surface }]}>
        <Button
          mode="outlined"
          onPress={handleAddToCart}
          style={styles.addToCartButton}
          disabled={!product.inStock}
        >
          {language.currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
        </Button>
        <Button
          mode="contained"
          onPress={handleBuyNow}
          style={styles.buyNowButton}
          disabled={!product.inStock}
        >
          {t('buyNow')}
        </Button>
      </Surface>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    paddingTop: Platform.OS === 'web' ? 10 : 40,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  imageSection: {
    backgroundColor: 'white',
  },
  mainImage: {
    width: '100%',
    height: 300,
    resizeMode: 'cover',
  },
  imageSelector: {
    padding: 10,
  },
  thumbnailContainer: {
    marginRight: 10,
    borderRadius: 8,
    overflow: 'hidden',
  },
  selectedThumbnail: {
    borderWidth: 2,
    borderColor: '#6200ee',
  },
  thumbnail: {
    width: 60,
    height: 60,
    resizeMode: 'cover',
  },
  infoSection: {
    margin: 15,
    padding: 20,
    borderRadius: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  titleRow: {
    marginBottom: 10,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  productName: {
    fontSize: 22,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 10,
  },
  categoryChip: {
    alignSelf: 'flex-start',
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  price: {
    fontSize: 24,
    fontWeight: 'bold',
    marginRight: 10,
  },
  originalPrice: {
    fontSize: 16,
    textDecorationLine: 'line-through',
    marginRight: 10,
  },
  discountChip: {
    backgroundColor: '#FF5722',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  rating: {
    fontSize: 16,
    marginRight: 5,
  },
  reviewCount: {
    fontSize: 14,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 20,
  },
  featuresSection: {
    marginBottom: 20,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  featureIcon: {
    color: '#4CAF50',
    marginRight: 10,
    fontSize: 16,
  },
  featureText: {
    fontSize: 14,
  },
  divider: {
    marginVertical: 20,
  },
  sellerSection: {
    marginBottom: 20,
  },
  sellerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  sellerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sellerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  sellerDetails: {
    flex: 1,
  },
  sellerNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sellerName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 5,
  },
  verifiedBadge: {
    color: '#4CAF50',
    fontSize: 16,
  },
  sellerRating: {
    fontSize: 12,
  },
  stockSection: {
    marginBottom: 10,
  },
  stockText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  actionBar: {
    flexDirection: 'row',
    padding: 15,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  addToCartButton: {
    flex: 1,
    marginRight: 10,
    borderRadius: 25,
  },
  buyNowButton: {
    flex: 1,
    marginLeft: 10,
    borderRadius: 25,
  },
});

export default ProductDetailScreen;
