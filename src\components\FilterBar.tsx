import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from '../i18n';
import { useDispatch, useSelector } from 'react-redux';
import { setFilters, clearFilters } from '../store/slices/storySlice';
import { RootState } from '../store';

const { width } = Dimensions.get('window');

interface FilterBarProps {
  showSearch?: boolean;
  onSearch?: (query: string) => void;
}

const categories = [
  'all',
  'fashion',
  'electronics',
  'home',
  'beauty',
  'sports',
  'toys',
  'automotive',
  'books',
  'food',
];

const FilterBar: React.FC<FilterBarProps> = ({ showSearch = true, onSearch }) => {
  const { t, isRTL } = useTranslation();
  const dispatch = useDispatch();
  const filters = useSelector((state: RootState) => (state.stories as any).filters);
  
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [tempFilters, setTempFilters] = useState({
    category: filters.category,
    priceRange: filters.priceRange,
    location: filters.location,
  });

  const handleCategorySelect = (category: string | null) => {
    dispatch(setFilters({ category: category === 'all' ? null : category }));
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (onSearch) {
      onSearch(text);
    }
  };

  const openFilterModal = () => {
    setTempFilters({
      category: filters.category,
      priceRange: filters.priceRange,
      location: filters.location,
    });
    setShowFilterModal(true);
  };

  const applyFilters = () => {
    dispatch(setFilters(tempFilters));
    setShowFilterModal(false);
  };

  const resetFilters = () => {
    dispatch(clearFilters());
    setTempFilters({
      category: null,
      priceRange: null,
      location: null,
    });
    setShowFilterModal(false);
  };

  const updateTempPriceRange = (min: string, max: string) => {
    const minValue = min ? parseInt(min, 10) : 0;
    const maxValue = max ? parseInt(max, 10) : 10000;
    setTempFilters({
      ...tempFilters,
      priceRange: [minValue, maxValue],
    });
  };

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      {showSearch && (
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, isRTL ? styles.textRTL : {}]}
            placeholder={t('search')}
            value={searchQuery}
            onChangeText={handleSearch}
            placeholderTextColor="#999"
          />
          <TouchableOpacity style={styles.filterButton} onPress={openFilterModal}>
            <Ionicons name="options-outline" size={20} color="#666" />
          </TouchableOpacity>
        </View>
      )}

      {/* Category Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesContainer}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryTab,
              filters.category === category || (category === 'all' && !filters.category)
                ? styles.activeCategory
                : {},
            ]}
            onPress={() => handleCategorySelect(category)}
          >
            <Text
              style={[
                styles.categoryText,
                filters.category === category || (category === 'all' && !filters.category)
                  ? styles.activeCategoryText
                  : {},
                isRTL ? styles.textRTL : {},
              ]}
            >
              {t(category)}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowFilterModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t('filter')}</Text>
              <TouchableOpacity onPress={() => setShowFilterModal(false)}>
                <Ionicons name="close" size={24} color="#000" />
              </TouchableOpacity>
            </View>

            {/* Price Range */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>{t('price')}</Text>
              <View style={styles.priceInputContainer}>
                <TextInput
                  style={styles.priceInput}
                  placeholder={t('min')}
                  keyboardType="numeric"
                  value={tempFilters.priceRange ? tempFilters.priceRange[0].toString() : ''}
                  onChangeText={(text) =>
                    updateTempPriceRange(
                      text,
                      tempFilters.priceRange ? tempFilters.priceRange[1].toString() : ''
                    )
                  }
                />
                <Text style={styles.priceSeparator}>-</Text>
                <TextInput
                  style={styles.priceInput}
                  placeholder={t('max')}
                  keyboardType="numeric"
                  value={tempFilters.priceRange ? tempFilters.priceRange[1].toString() : ''}
                  onChangeText={(text) =>
                    updateTempPriceRange(
                      tempFilters.priceRange ? tempFilters.priceRange[0].toString() : '',
                      text
                    )
                  }
                />
              </View>
            </View>

            {/* Location */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>{t('location')}</Text>
              <TextInput
                style={styles.locationInput}
                placeholder={t('enterLocation')}
                value={tempFilters.location || ''}
                onChangeText={(text) =>
                  setTempFilters({ ...tempFilters, location: text.length > 0 ? text : null })
                }
              />
            </View>

            {/* Action Buttons */}
            <View style={styles.modalActions}>
              <TouchableOpacity style={styles.resetButton} onPress={resetFilters}>
                <Text style={styles.resetButtonText}>{t('reset')}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.applyButton} onPress={applyFilters}>
                <Text style={styles.applyButtonText}>{t('apply')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    paddingTop: 10,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 15,
    marginBottom: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 25,
    paddingHorizontal: 15,
    height: 40,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 14,
    color: '#333',
  },
  textRTL: {
    textAlign: 'right',
  },
  filterButton: {
    padding: 5,
  },
  categoriesContainer: {
    paddingHorizontal: 10,
    paddingBottom: 5,
  },
  categoryTab: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginHorizontal: 5,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  activeCategory: {
    backgroundColor: '#6200ee',
  },
  categoryText: {
    fontSize: 14,
    color: '#666',
  },
  activeCategoryText: {
    color: 'white',
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: Dimensions.get('window').height * 0.8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterSection: {
    marginBottom: 20,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  priceSeparator: {
    marginHorizontal: 10,
  },
  locationInput: {
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  resetButton: {
    flex: 1,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#6200ee',
    borderRadius: 8,
    marginRight: 10,
  },
  resetButtonText: {
    color: '#6200ee',
    fontWeight: 'bold',
  },
  applyButton: {
    flex: 1,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#6200ee',
    borderRadius: 8,
    marginLeft: 10,
  },
  applyButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default FilterBar;